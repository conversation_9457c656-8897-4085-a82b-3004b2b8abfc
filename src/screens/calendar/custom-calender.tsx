import dayjs from 'dayjs';
import React, { useEffect, useRef } from 'react';
import {
    Dimensions,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

import tw from '~/styles/tailwind';

import { getCategoryColor } from './calendar-screen';

const TIME_SLOTS = Array.from(
    { length: 24 },
    (_, i) => `${i.toString().padStart(2, '0')}:00`
);

const getMinutesSinceMidnight = (time: string | Date) => {
    // Parse the time without timezone conversion to avoid offset issues
    const date = dayjs(time);
    return date.hour() * 60 + date.minute();
};

const CustomTrainerCalendarList = ({
    selectedDate,
    trainers = [],
    events = [],
    selectedTrainer,
    onTrainerPress,
    onEventPress,
}: any) => {
    const selectedDateStr = dayjs(selectedDate).format('YYYY-MM-DD');
    const scrollRefs = useRef<{ [key: string]: ScrollView | null }>({});

    const screenWidth = Dimensions.get('window').width;
    const trainerColumnWidth = screenWidth / 5;

    useEffect(() => {
        const yOffset = 7 * 90;
        Object.values(scrollRefs.current).forEach((ref) => {
            ref?.scrollTo({ y: yOffset, animated: false });
        });
    }, []);

    const getTrainerColumnWidth = () => {
        const count = trainers.length;

        if (count === 1) return screenWidth - 60;
        if (count === 2) return (screenWidth - 60) / 2;
        return 130;
    };

    console.log('events-------', events);

    const assignEventColumns = (events: any[]) => {
        const sortedEvents = [...events].sort(
            (a, b) =>
                getMinutesSinceMidnight(a.start) -
                getMinutesSinceMidnight(b.start)
        );

        const result = [];
        const columns: any[] = [];

        for (const event of sortedEvents) {
            const start = getMinutesSinceMidnight(event.start);
            const end = getMinutesSinceMidnight(event.end);

            let placed = false;

            for (let i = 0; i < columns.length; i++) {
                const col = columns[i];
                if (start >= getMinutesSinceMidnight(col[col.length - 1].end)) {
                    col.push(event);
                    result.push({
                        ...event,
                        columnIndex: i,
                        totalColumns: columns.length,
                    });
                    placed = true;
                    break;
                }
            }

            if (!placed) {
                columns.push([event]);
                result.push({
                    ...event,
                    columnIndex: columns.length - 1,
                    totalColumns: columns.length,
                });
            }
        }

        return result.map((e) => ({
            ...e,
            totalColumns: columns.length,
        }));
    };

    return (
        <View style={{ flexDirection: 'row' }}>
            {/* Time Column */}
            {/* <View
                style={[
                    styles.timeColumn,
                    {
                        position: 'absolute',
                        left: 0,
                        top: 0,
                        bottom: 0,
                        zIndex: 10,
                    },
                ]}
            >
                {TIME_SLOTS.map((slot) => (
                    <View key={slot} style={styles.timeCell}>
                        <Text style={styles.timeText}>{slot}</Text>
                    </View>
                ))}
            </View> */}

            {/* Trainers' Columns */}
            <ScrollView horizontal contentContainerStyle={{}}>
                {trainers.map((trainer: any) => {
                    const rawTrainerEvents = events.filter(
                        (e: any) =>
                            e.trainerId === trainer.userId &&
                            dayjs(e.start).format('YYYY-MM-DD') ===
                                selectedDateStr
                    );
                    const trainerEvents = assignEventColumns(rawTrainerEvents);

                    return (
                        <View
                            key={trainer.userId}
                            style={[
                                styles.trainerColumn,
                                // tw`w-${getTrainerColumnWidth()}`,
                                { width: trainerColumnWidth },
                            ]}
                        >
                            {/* Sticky Trainer Header */}
                            <View style={styles.trainerHeaderSticky}>
                                <TouchableOpacity
                                    onPress={() => onTrainerPress?.(trainer)}
                                >
                                    <View style={[tw``, styles.trainerAvatar]}>
                                        <Text style={styles.trainerAvatarText}>
                                            {(trainer.firstName
                                                ?.charAt(0)
                                                .toUpperCase() || '') +
                                                (trainer.lastName
                                                    ?.charAt(0)
                                                    .toUpperCase() || '')}
                                        </Text>
                                    </View>
                                </TouchableOpacity>
                            </View>

                            {/* Timeline with Events */}
                            <ScrollView
                                ref={(ref) => {
                                    scrollRefs.current[trainer.userId] = ref;
                                }}
                                showsVerticalScrollIndicator={false}
                                style={{ height: 24 * 90, marginTop: 40 }}
                                contentContainerStyle={{ height: 24 * 90 }}
                            >
                                <View
                                    style={{
                                        height: 24 * 90,
                                        position: 'relative',
                                        marginTop: 40,
                                    }}
                                >
                                    {trainerEvents.map((event: any) => {
                                        const startMin =
                                            getMinutesSinceMidnight(
                                                event.start
                                            );
                                        const endMin = getMinutesSinceMidnight(
                                            event.end
                                        );
                                        const duration = endMin - startMin;

                                        const topPosition =
                                            (startMin / 60) * 90;
                                        const blockHeight = Math.max(
                                            (duration / 60) * 90,
                                            15
                                        );
                                        const eventColor = getCategoryColor(
                                            event.classType
                                        );
                                        const totalColumns =
                                            event.totalColumns || 1;
                                        const columnIndex =
                                            event.columnIndex || 0;

                                        const eventWidth =
                                            totalColumns === 1
                                                ? trainerColumnWidth - 10
                                                : (trainerColumnWidth - 10) /
                                                  totalColumns;

                                        const leftOffset =
                                            totalColumns === 1
                                                ? 5
                                                : columnIndex * eventWidth + 5;

                                        return (
                                            <TouchableOpacity
                                                key={event.id}
                                                onPress={() =>
                                                    onEventPress?.(event)
                                                }
                                                style={[
                                                    styles.eventBlock,
                                                    {
                                                        top: topPosition,
                                                        height: blockHeight,
                                                        width: eventWidth - 5,
                                                        left: leftOffset,
                                                        backgroundColor:
                                                            eventColor,
                                                        borderColor: eventColor,
                                                    },
                                                ]}
                                            >
                                                <Text
                                                    style={styles.eventText}
                                                    numberOfLines={2}
                                                >
                                                    {event.title}
                                                </Text>
                                                {duration > 15 &&
                                                    event.clientName && (
                                                        <Text
                                                            numberOfLines={1}
                                                            style={
                                                                styles.timeRange
                                                            }
                                                        >
                                                            {event.clientName}
                                                        </Text>
                                                    )}
                                                <Text
                                                    numberOfLines={2}
                                                    style={styles.timeRange}
                                                >
                                                    {event.from} - {event.to}
                                                </Text>
                                            </TouchableOpacity>
                                        );
                                    })}

                                    {/* Hour Grid Lines */}
                                    {TIME_SLOTS.map((slot, index) => (
                                        <View
                                            key={`grid-${slot}`}
                                            style={[
                                                styles.gridLine,
                                                {
                                                    top: index * 90,
                                                },
                                            ]}
                                        />
                                    ))}

                                    {/* Empty Slot Click Handler */}
                                    {[...Array(24)].map((_, index) => {
                                        const hour = `${index
                                            .toString()
                                            .padStart(2, '0')}:00`;
                                        return (
                                            <TouchableOpacity
                                                key={hour}
                                                style={[
                                                    styles.emptyCell,
                                                    {
                                                        top: index * 90,
                                                    },
                                                ]}
                                                onPress={() =>
                                                    onEventPress?.({
                                                        isEmpty: true,
                                                        slot: hour,
                                                        trainer,
                                                        selectedDate,
                                                    })
                                                }
                                            />
                                        );
                                    })}
                                </View>
                            </ScrollView>
                        </View>
                    );
                })}
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    timeColumn: {
        width: 60,
        paddingTop: 40,
        backgroundColor: '#EEE',
    },
    timeCell: {
        height: 90,
        justifyContent: 'center',
        alignItems: 'center',
        borderBottomWidth: 0.5,
        borderColor: '#DDD',
    },
    timeText: {
        fontSize: 12,
        color: '#333',
    },
    trainerColumn: {
        borderLeftWidth: 0.5,
        borderColor: '#DDD',
        position: 'relative',
        marginLeft: -6,
    },
    trainerAvatar: {
        width: 36,
        height: 36,
        borderRadius: 18,
        backgroundColor: '#0fd99c',
        justifyContent: 'center',
        alignItems: 'center',
    },
    trainerAvatarText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 16,
    },
    trainerHeaderSticky: {
        height: 40,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f1f1f1',
        zIndex: 20,
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
    },
    eventBlock: {
        position: 'absolute',
        left: 5,
        right: 5,
        borderRadius: 6,
        paddingTop: 1,
        paddingLeft: 5,
        paddingBottom: 1,
        borderWidth: 0.5,
        zIndex: 5,
    },
    eventText: {
        fontSize: 13,
        fontWeight: '600',
    },
    timeRange: {
        fontSize: 11,
        color: '#555',
    },
    emptyCell: {
        position: 'absolute',
        left: 0,
        right: 0,
        height: 90,
        borderBottomWidth: 0,
        borderColor: '#DDD',
    },
    gridLine: {
        position: 'absolute',
        left: 0,
        right: 0,
        height: 1,
        backgroundColor: '#DDD',
    },
});

export default CustomTrainerCalendarList;
