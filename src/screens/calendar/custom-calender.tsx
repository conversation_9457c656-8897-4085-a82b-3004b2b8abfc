import dayjs from 'dayjs';
import React, { useEffect, useMemo, useRef } from 'react';
import {
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

import tw from '~/styles/tailwind';

const TIME_SLOTS = Array.from({ length: 24 }, (_, i) => {
    return `${i.toString().padStart(2, '0')}:00`;
});

const CustomTrainerCalendarList = ({
    selectedDate,
    trainers = [],
    events = [],
    selectedTrainer,
    onTrainerPress,
    onEventPress,
}: any) => {
    console.log('events---------', events);

    const timeScrollRef = useRef<ScrollView>(null);
    const trainerScrollRefs = useRef<Record<string, ScrollView | null>>({});

    const formattedEvents = useMemo(() => {
        const result: Record<string, Record<string, any>> = {};
        trainers.forEach((trainer: any) => {
            result[trainer.userId] = {};
            TIME_SLOTS.forEach((slot) => {
                result[trainer.userId][slot] = null;
            });
        });

        const selectedDateStr = dayjs(selectedDate).format('YYYY-MM-DD');

        events?.forEach((event: any) => {
            if (dayjs(event.start).format('YYYY-MM-DD') !== selectedDateStr) {
                return;
            }

            const hour = dayjs(event.start).local().format('HH:00');

            if (result?.[event.trainerId]?.[hour] !== undefined) {
                result[event.trainerId][hour] = event;
            }
        });

        return result;
    }, [events, trainers, selectedDate]);

    console.log('formattedEvents--------', formattedEvents);

    // Calculate current time and scroll position
    const currentHour = dayjs().hour();
    const scrollToCurrentTime = () => {
        const cellHeight = 90; // Height of each time cell
        const scrollPosition = currentHour * cellHeight;

        // Scroll time column
        timeScrollRef.current?.scrollTo({
            y: scrollPosition,
            animated: true,
        });

        // Scroll all trainer columns
        Object.values(trainerScrollRefs.current).forEach((ref) => {
            ref?.scrollTo({
                y: scrollPosition,
                animated: true,
            });
        });
    };

    // Auto-scroll to current time when component mounts or date changes
    useEffect(() => {
        const timer = setTimeout(() => {
            scrollToCurrentTime();
        }, 100); // Small delay to ensure components are rendered

        return () => clearTimeout(timer);
    }, [selectedDate]);

    return (
        <View style={{ flexDirection: 'row' }}>
            <View
                style={[
                    styles.timeColumn,
                    {
                        position: 'absolute',
                        left: 0,
                        top: 0,
                        bottom: 0,
                        zIndex: 10,
                    },
                ]}
            >
                <View style={styles.timeHeaderSticky}>
                    <Text style={styles.timeHeaderText}>Time</Text>
                </View>
                <ScrollView
                    ref={timeScrollRef}
                    showsVerticalScrollIndicator={false}
                    onScroll={(event) => {
                        const scrollY = event.nativeEvent.contentOffset.y;
                        // Sync scroll with trainer columns
                        Object.values(trainerScrollRefs.current).forEach(
                            (ref) => {
                                ref?.scrollTo({
                                    y: scrollY,
                                    animated: false,
                                });
                            }
                        );
                    }}
                    scrollEventThrottle={16}
                >
                    {TIME_SLOTS.map((slot) => (
                        <View key={slot} style={styles.timeCell}>
                            <Text style={styles.timeText}>{slot}</Text>
                        </View>
                    ))}
                </ScrollView>
            </View>

            <ScrollView horizontal contentContainerStyle={{ paddingLeft: 60 }}>
                {trainers.map((trainer: any) => (
                    <View key={trainer.userId} style={styles.trainerColumn}>
                        <View style={styles.trainerHeaderSticky}>
                            <TouchableOpacity
                                onPress={() => onTrainerPress?.(trainer)}
                            >
                                <View style={[tw``, styles.trainerAvatar]}>
                                    <Text style={styles.trainerAvatarText}>
                                        {(trainer.firstName
                                            ?.charAt(0)
                                            .toUpperCase() || '') +
                                            (trainer.lastName
                                                ?.charAt(0)
                                                .toUpperCase() || '')}
                                    </Text>
                                </View>
                            </TouchableOpacity>
                        </View>
                        <ScrollView
                            ref={(ref) => {
                                trainerScrollRefs.current[trainer.userId] = ref;
                            }}
                            showsVerticalScrollIndicator={false}
                            onScroll={(event) => {
                                const scrollY =
                                    event.nativeEvent.contentOffset.y;
                                // Sync scroll with time column
                                timeScrollRef.current?.scrollTo({
                                    y: scrollY,
                                    animated: false,
                                });
                                // Sync scroll with other trainer columns
                                Object.entries(
                                    trainerScrollRefs.current
                                ).forEach(([userId, ref]) => {
                                    if (userId !== trainer.userId && ref) {
                                        ref.scrollTo({
                                            y: scrollY,
                                            animated: false,
                                        });
                                    }
                                });
                            }}
                            scrollEventThrottle={16}
                        >
                            {TIME_SLOTS.map((slot) => {
                                const event =
                                    formattedEvents?.[trainer.userId]?.[slot];
                                return (
                                    <TouchableOpacity
                                        key={slot}
                                        onPress={() => onEventPress?.(event)}
                                    >
                                        <View
                                            style={[
                                                styles.eventCell,
                                                {
                                                    backgroundColor: event
                                                        ? '#DDEEFF'
                                                        : '#F5F5F5',
                                                },
                                            ]}
                                        >
                                            {event ? (
                                                <>
                                                    <Text
                                                        style={styles.eventText}
                                                    >
                                                        {event.title}
                                                    </Text>
                                                    <Text
                                                        style={styles.timeRange}
                                                    >
                                                        {dayjs(
                                                            event.start
                                                        ).format('h:mm A')}{' '}
                                                        -{' '}
                                                        {dayjs(
                                                            event.end
                                                        ).format('h:mm A')}
                                                    </Text>
                                                </>
                                            ) : null}
                                        </View>
                                    </TouchableOpacity>
                                );
                            })}
                        </ScrollView>
                    </View>
                ))}
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    timeColumn: {
        width: 60,
        paddingTop: 40,
        backgroundColor: '#EEE',
    },
    timeCell: {
        height: 90,
        justifyContent: 'center',
        alignItems: 'center',
    },
    trainerAvatar: {
        width: 36,
        height: 36,
        borderRadius: 18,
        backgroundColor: '#8143D1',
        justifyContent: 'center',
        alignItems: 'center',
    },
    trainerAvatarText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 18,
    },
    timeText: {
        fontSize: 12,
        color: '#333',
    },
    trainerColumn: {
        width: 110,
        borderLeftWidth: 1,
        borderColor: '#DDD',
    },
    trainerHeader: {
        height: 40,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f1f1f1',
        paddingTop: 40, // To offset the fixed header
    },
    trainerText: {
        color: '#fff',
        fontWeight: 'bold',
    },
    eventCell: {
        height: 90,
        justifyContent: 'center',
        paddingHorizontal: 4,
        borderBottomWidth: 1,
        borderColor: '#DDD',
    },
    eventText: {
        fontSize: 13,
        fontWeight: '600',
    },
    timeRange: {
        fontSize: 11,
        color: '#555',
    },
    trainerHeaderSticky: {
        height: 40,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#f1f1f1',
        zIndex: 20,
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
    },
});

export default CustomTrainerCalendarList;
