import { useFocusEffect } from '@react-navigation/native';
import dayjs from 'dayjs';
import moment from 'moment';
import React, {
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState,
} from 'react';
import {
    Dimensions,
    FlatList,
    RecursiveArray,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
    ViewStyle,
} from 'react-native';
import {
    Calendar as BigCalendar,
    EventRenderer,
} from 'react-native-big-calendar';
import CalendarStrip from 'react-native-calendar-strip';
import { Calendar } from 'react-native-calendars';
import Svg, { Path } from 'react-native-svg';
import { GetTrainersListByOrganization } from '~/redux/actions/common-actions';
import {
    BookedCalendarData,
    DeleteTimeSlot,
    GetStaffAvailabilityList,
    processScheduleData,
} from '~/redux/actions/scheduling-actions';
import { SelectTrainer } from '~/redux/slices/scheduling-slice';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import { UserRole } from '~/constants/enums';
import { SELECT_LOCATION_SCREEN } from '~/constants/navigation-constant';

import AvailabilityModal from '~/components/common/availibility-modal';
import BackButtonHeading from '~/components/common/back-button-heading';
import ConfirmActionDeleteModal from '~/components/common/modals/confirm-delete-action';
import FilterModal from '~/components/common/modals/filter-modal';
import TrainerByOrgModal from '~/components/common/modals/trainers-by-org-modal';
import AvailableSlotModal from '~/components/modals/availability-popup';
import BookingSlotModal from '~/components/modals/booking-popup';
import DeleteCustomRangeModal from '~/components/modals/delete-custom-daterange';
import UnavailableSlotModal from '~/components/modals/unavailable-slot-modal';

import {
    capitalizeFirstLetter,
    serviceTypes,
    serviceTypesCaps,
} from '~/scripts/function';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { navigationRef } from '~/hooks/useLocation';

import CustomTrainerCalendarList from './custom-calender';

type ViewMode = 'list' | 'day' | 'week' | 'room';

export interface ICalendarEventBase {
    start: Date;
    end: Date;
    title?: string; // Make title optional
    children?: any | null;
    hideHours?: boolean;
    disabled?: boolean;
    overlapPosition?: number;
    overlapCount?: number;
}

export function formatStartEnd(
    start: Date,
    end: Date,
    format: string = 'h:mm A'
) {
    return `${dayjs(start).format(format)} - ${dayjs(end).format(format)}`;
}

export interface MyCustomEventType extends ICalendarEventBase {
    color?: string;
    available?: boolean; // Add available property
    unavailable?: boolean;
}

const Icon = ({ children, ...props }: any) => (
    <Svg
        width={20}
        height={20}
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        {...props}
    >
        {children}
    </Svg>
);

const LocationIcon = React.memo((props: any) => (
    <Icon {...props}>
        <Path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0118 0z" />
        <Path d="M12 13a3 3 0 100-6 3 3 0 000 6z" />
    </Icon>
));

const ChevronLeft = React.memo((props: any) => (
    <Icon {...props}>
        <Path d="M15 18l-6-6 6-6" />
    </Icon>
));

const ChevronRight = React.memo((props: any) => (
    <Icon {...props}>
        <Path d="M9 18l6-6-6-6" />
    </Icon>
));

const PlusIcon: any = React.memo((props: any) => (
    <Svg
        width={24}
        height={24}
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
        {...props}
    >
        <Path d="M12 5v14M5 12h14" />
    </Svg>
));

function renderViewMode(viewMode: string) {
    if (viewMode === 'list') return 'schedule';
    if (viewMode === 'day') return 'day';
    if (viewMode === 'week') return 'week';
    if (viewMode === 'room') return 'room';
    return 'day';
}

export const getCategoryColor = (classType: any) => {
    const colors: any = {
        personalAppointment: '#A7C7E7',
        bookings: '#CCE2C9',
        courses: '#F5E3C9',
        classes: '#B39DDB',
    };
    return colors[classType] || '#E0E0E0';
};

export const getCategoryShadowColor = (classType: any) => {
    const colors: any = {
        personalAppointment: 'rgba(62, 121, 247, 0.05)',
        bookings: 'rgba(204, 226, 201, 0.2)',
        courses: 'rgba(245, 227, 201, 0.2)',
        classes: 'rgba(179, 157, 219, 0.15)',
    };
    return colors[classType] || '#E0E0E0';
};

const groupByDate = (data: any) => {
    return data.reduce((acc: any, event: any) => {
        const dateKey = dayjs(event.start).format('YYYY-MM-DD');
        if (!acc[dateKey]) acc[dateKey] = [];
        acc[dateKey].push(event);
        return acc;
    }, {});
};

const CalendarScreen = ({ navigation }: any) => {
    const [events, setEvents] = useState<any[]>([]);
    const [bookingList, setBookingList] = useState<any[]>([]);
    const [showUnavailableModal, setUnavailableModalVisible] =
        useState<boolean>(false);
    const [showAvailablePopupModal, setAvailablePopupModal] =
        useState<boolean>(false);
    const [showBookedPopupModal, setBookedPopupModal] =
        useState<boolean>(false);
    const [event, setEvent] = useState<any>({});
    const [showDeleteModal, setShowDeleteModal] = useState<any>(false);
    const [showDeleteCustomRangeModal, setDeleteCustomRangeModal] =
        useState(false);
    const dispatch = useAppDispatch();
    const [showModal, setModalVisible] = useState<boolean>(false);
    const [showTrainerListModal, setShowTrainerListModal] =
        useState<boolean>(false);
    const [viewMode, setViewMode] = useState<ViewMode>('list');
    const [selectedDate, setSelectedDate] = useState<any>(
        dayjs().format('YYYY-MM-DD')
    );
    const [startDate, setStartDate] = useState(null);
    const [endDate, setEndDate] = useState(null);
    const [isCalendarMinimized, setIsCalendarMinimized] =
        useState<boolean>(true);
    const [dateRange, setDateRange] = useState<any>([]);
    const lastFetchedDate = useRef<string | null>(null);

    const isMounted = useRef(false);
    // Add a ref to track ongoing API calls
    const isLoadingData = useRef(false);

    const [filterModal, setFilterModal] = useState(false);
    const [roomOrInstructor, setRoomOrInstructor] = useState<string | null>(
        'staff'
    );
    const [serviceType, setServiceType] = useState<string | null>(null);
    const [activeDateForModal, setActiveDateForModal] =
        useState<string>(selectedDate);

    const [visibleTrainers, setVisibleTrainers] = useState(0); // starting index
    const [selectedTrainerId, setSelectedTrainerId] = useState(null);

    const openFilterModal = () => {
        setFilterModal(true);
    };
    const hideModal = () => {
        setFilterModal(false);
    };

    useFocusEffect(
        useCallback(() => {
            setActiveDateForModal(selectedDate);
        }, [selectedDate])
    );

    const store = useAppSelector((state) => ({
        facilityName: state.facility_store.facility?.name,
        facilityId: state.facility_store.facility._id,
        availableSlots: state.scheduling_store.availabilitySlots?.[0]?.schedule,
        currentSlot: state.scheduling_store.availabilitySlots?.[0],
        userRole: state.auth_store.role,
        selectedTrainer: state.scheduling_store.selectedTrainer,
        calendarSchedulingList: state.scheduling_store.calendarSchedulingList,
        trainers: state.scheduling_store.trainersListByOrganization,
    }));

    console.log('Store value or booked clender---------', roomOrInstructor);

    useFocusEffect(
        useCallback(() => {
            dispatch(
                BookedCalendarData({
                    filterType: viewMode,
                    // classType: 'personalAppointment',
                    // facilityId: [store.facilityId],
                    // trainerId: store.selectedTrainer?.userId,
                    // roomId: selectedRooms,
                    // serviceCategory: selectedCategories,
                })
            );
        }, [
            startDate,
            endDate,
            selectedDate,
            store.selectedTrainer,
            roomOrInstructor,
            viewMode,
        ])
    );

    const updateDateRange = useCallback((date: any) => {
        const start = dayjs(date).subtract(3, 'days');
        const end = dayjs(date).add(3, 'days');
        const range: any[] = [];
        for (
            let d = start;
            d.isBefore(end) || d.isSame(end, 'day');
            d = d.add(1, 'day')
        ) {
            range.push(d.format('YYYY-MM-DD'));
        }
        setDateRange(range);
    }, []);

    const renderSelectedDate = useCallback(
        (dateString: string) => dayjs(dateString).format('dddd D MMMM YYYY'),
        []
    );

    const renderRange = () => {
        if (!startDate || !endDate) return '';

        const start = dayjs(startDate);
        const end = dayjs(endDate);

        // Format as "11 Nov - 15 Nov"
        const formattedRange = `${start.format('DD MMM')} - ${end.format(
            'DD MMM'
        )}`;
        return formattedRange;
    };

    const formatDate = useCallback(
        (dateString: string) => ({
            date: dayjs(dateString).format('D'),
            day: dayjs(dateString).format('ddd'),
        }),
        []
    );

    const renderDateItem = useCallback(
        ({ item }: any) => {
            const { date, day } = formatDate(item);
            const isSelected = item === selectedDate;

            return (
                <TouchableOpacity
                    style={[
                        styles.dateItem,
                        isSelected && styles.selectedDateItem,
                    ]}
                    onPress={() => setSelectedDate(item)}
                >
                    <View style={styles.dateItemContainer}>
                        <Text
                            style={[
                                styles.dateItemText,
                                isSelected && styles.selectedDateItemText,
                            ]}
                        >
                            {date}
                        </Text>
                        <Text
                            style={[
                                styles.dayItemText,
                                isSelected && styles.selectedDayItemText,
                            ]}
                        >
                            {day}
                        </Text>
                    </View>
                </TouchableOpacity>
            );
        },
        [selectedDate, formatDate]
    );

    const handleLocation = useCallback(() => {
        navigation.navigate(SELECT_LOCATION_SCREEN, {
            selectTrainerLocation: true,
        });
    }, [navigation]);

    const closeModal = useCallback(() => setModalVisible(false), []);

    const selectedWeekDates = useMemo(() => {
        const startOfWeek = dayjs(selectedDate).startOf('week');
        return Array.from({ length: 7 }, (_, i) =>
            startOfWeek.add(i, 'day').format('YYYY-MM-DD')
        );
    }, [selectedDate]);

    const filteredWeekBookingEvents = useMemo(() => {
        if (!bookingList || bookingList.length === 0) return [];

        return bookingList.filter((event) => {
            if (!event.start) return false;
            const eventDate = dayjs(event.start).format('YYYY-MM-DD');
            return selectedWeekDates.includes(eventDate);
        });
    }, [bookingList, selectedWeekDates]);

    const filteredEvents = useMemo(() => {
        if (viewMode === 'list') {
            return events.filter(
                (event) =>
                    dayjs(event.start).format('YYYY-MM-DD') === selectedDate
            );
        }
        return events;
    }, [events, selectedDate, viewMode]);

    const filteredBookingEvent = useMemo(() => {
        if (!bookingList || bookingList.length === 0) return [];

        return bookingList.filter((event) => {
            if (!event.start) return false;

            const eventDate = dayjs(event.start).format('YYYY-MM-DD');
            const selectedFormattedDate =
                dayjs(selectedDate).format('YYYY-MM-DD');

            return eventDate === selectedFormattedDate;
        });
    }, [bookingList, selectedDate]);

    function deleteTimeSlot() {
        const dateId = store.currentSlot?.schedule?.[0]?.dateId;
        const payload = {
            type: 'slot',
            dateId,
            slotId: event.slotId,
        };

        dispatch(
            DeleteTimeSlot({
                payload,
                selectedDate,
                facilityId: store.facilityId,
            })
        )
            .unwrap()
            .then(() => {
                // console.log('delete the time slit');
                setShowDeleteModal(false);
            });
    }

    function deleteCustomRangeSlot(startDate: any, endDate: any) {
        const { userId, facilityId } = store.currentSlot?._id;

        const start = new Date(startDate);
        start.setUTCHours(0, 0, 0, 0);

        const end = new Date(endDate);
        end.setUTCHours(23, 59, 59, 999);

        const startDateUTC = start.toISOString();
        const endDateUTC = end.toISOString();
        const payload = {
            type: 'custom',
            startDate: startDateUTC,
            endDate: endDateUTC,
            userId,
            facilityId,
        };

        dispatch(DeleteTimeSlot({ payload }))
            .unwrap()
            .then(() => setDeleteCustomRangeModal(false));
    }

    function showTrainerModal() {
        setShowTrainerListModal(!showTrainerListModal);
    }

    function handlePress(event: any) {
        console.log('Slot clicked:', event);
        if (event.available === true) {
            setAvailablePopupModal(true);
        } else if (event.unavailable === true) {
            setUnavailableModalVisible(true);
        } else if (event.isBooking === true) {
            setBookedPopupModal(true);
        }
        setEvent(event);
    }

    const handleTopDayDateChange = useCallback(
        async (date: any) => {
            // Update the selectedDate
            setSelectedDate(date);

            // Trigger the API to fetch the availability list for the new selected date
            if (!isLoadingData.current) {
                isLoadingData.current = true;

                // Adjust start and end dates for API
                const startDate = moment(date).startOf('day').format();
                const endDate = moment(date).endOf('day').format();

                await dispatch(
                    GetStaffAvailabilityList({
                        selectedDate: date,
                        facilityId: store.facilityId,
                        manipulateStartDate: startDate,
                        manipulateEndDate: endDate,
                    })
                ).unwrap();

                isLoadingData.current = false;
            }
        },
        [dispatch, store.facilityId]
    );

    const handleDateChange = useCallback(
        async (range: any) => {
            if (isLoadingData.current) return;

            const startDate = range[0];
            const endDate = range[range.length - 1];
            setStartDate(startDate);
            setEndDate(endDate);

            if (lastFetchedDate.current === startDate) return;

            isLoadingData.current = true;
            try {
                const manipulateStartDate = moment(startDate)
                    .clone()
                    .startOf('day')
                    .format();
                const manipulateEndDate = moment(endDate)
                    .clone()
                    .endOf('day')
                    .format();

                await dispatch(
                    GetStaffAvailabilityList({
                        selectedDate: startDate,
                        facilityId: store.facilityId,
                        viewMode,
                        manipulateStartDate,
                        manipulateEndDate,
                    })
                ).unwrap();

                lastFetchedDate.current = startDate;
            } finally {
                isLoadingData.current = false;
            }
        },
        [dispatch, store.facilityId, viewMode]
    );

    useFocusEffect(
        useCallback(() => {
            const fetchData = async () => {
                if (isLoadingData.current) return;
                // if (!isMounted.current) {
                //     isMounted.current = true;
                //     return;
                // }

                if (viewMode === 'day' || viewMode === 'list') {
                    if (lastFetchedDate.current === selectedDate) return;

                    isLoadingData.current = true;
                    try {
                        await dispatch(
                            GetStaffAvailabilityList({
                                selectedDate,
                                facilityId: store.facilityId,
                            })
                        ).unwrap();

                        lastFetchedDate.current = selectedDate;
                    } finally {
                        isLoadingData.current = false;
                    }
                }
            };

            fetchData();

            return () => {
                setAvailablePopupModal(false);
                setBookedPopupModal(false);
                setUnavailableModalVisible(false);
                setModalVisible(false);
            };
        }, [
            dispatch,
            store.facilityId,
            selectedDate,
            store.selectedTrainer,
            viewMode,
            navigation,
            dispatch,
            roomOrInstructor,
        ])
    );

    // Add cleanup effect
    useEffect(() => {
        return () => {
            isMounted.current = false;
            isLoadingData.current = false;
            lastFetchedDate.current = null;
        };
    }, []);

    const memoizedUpdateDateRange = useMemo(() => updateDateRange, []);
    useEffect(() => {
        memoizedUpdateDateRange(selectedDate);
    }, [selectedDate]);
    console.log(
        'store.calendarSchedulingList----------',
        store.calendarSchedulingList
    );

    useEffect(() => {
        const availabilityEvents = processScheduleData(store?.availableSlots);

        const appointmentEvents =
            store.calendarSchedulingList?.map((item) => ({
                id: item.id,
                title: item.title,
                clientName: item.clientName,
                start: new Date(item.start),
                end: new Date(item.end),
                color: getCategoryColor(item.classType),
                classType: item.classType,
                priority: 2,
                isBooking: true,
            })) || [];

        // Assign lower priority for availability/unavailability
        const layeredEvents = [
            ...availabilityEvents.map((event) => ({ ...event, priority: 1 })),
            ...appointmentEvents,
        ];

        // Sort events to ensure appointments appear on top
        setEvents(layeredEvents?.sort((a, b) => b.priority - a.priority));
    }, [store.availableSlots, store.calendarSchedulingList]);

    useEffect(() => {
        if (!store.calendarSchedulingList) return;

        const bookedEvents =
            store.calendarSchedulingList
                ?.map((item) => {
                    if (!item?.date || !item?.from || !item?.to) {
                        console.warn('Skipping invalid booking item:', item);
                        return null;
                    }

                    return {
                        ...item,
                        id: item.id,
                        title: item.title ?? 'No Title',
                        clientName: item.clientName ?? '',
                        start: new Date(
                            `${item.date.split('T')[0]}T${item.from}:00`
                        ),
                        end: new Date(
                            `${item.date.split('T')[0]}T${item.to}:00`
                        ),
                        color: getCategoryColor(item.classType),
                        classType: item.classType ?? 'Unknown Type',
                        isBooking: true,
                        priority: 2,
                    };
                })
                ?.filter(Boolean) || [];

        setBookingList(
            bookedEvents.sort((a: any, b: any) => a.start - b.start)
        );
    }, [store.calendarSchedulingList, selectedDate]);

    useFocusEffect(
        useCallback(() => {
            if (
                store.userRole === UserRole.WEB_MASTER ||
                store.userRole === UserRole.ORGANIZATION ||
                store.userRole === UserRole.FRONT_DESK_ADMIN
            ) {
                dispatch(
                    GetTrainersListByOrganization({
                        facilityId: store.facilityId,
                    })
                );
            }
        }, [store.facilityId])
    );

    const customEventRenderer: EventRenderer<MyCustomEventType | any> = (
        event,
        touchableOpacityProps
    ) => {
        let backgroundColor = event.color || 'orange';

        if (event.available) {
            backgroundColor = '#e8f5f3';
        } else if (event.unavailable) {
            backgroundColor = '#f5f5f5';
        }

        return (
            <TouchableOpacity
                {...touchableOpacityProps}
                style={[
                    ...(touchableOpacityProps.style as RecursiveArray<ViewStyle>),
                    {
                        backgroundColor,
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: 5,
                        padding: 5,
                        opacity: event.classType ? 1 : 0.6,
                        // elevation: event.prioirty ? 5 : 1,
                        zIndex: event?.isBooking ? 10 : 1,
                    },
                ]}
            >
                {event.classType && event.isBooking && (
                    <>
                        {/* <Text style={{ color: 'black', fontWeight: 'bold' }}>
                            {event.clientName}
                        </Text> */}
                        <Text style={{ color: 'black', fontWeight: 'bold' }}>
                            {event.title}
                        </Text>
                        <Text style={{ color: 'black' }}>
                            {formatStartEnd(event.start, event.end)}
                        </Text>
                    </>
                )}
            </TouchableOpacity>
        );
    };

    // const handleDateChange = (date) => {
    //     const formattedDate = moment(date); // Ensure moment object
    //     setSelectedDate(formattedDate);

    //     if (onDateSelected) {
    //         onDateSelected(formattedDate); // Pass moment object to parent
    //     }
    // };

    let datesWhitelist = [
        {
            start: moment().subtract(1000, 'days'), // Start from today
            end: moment().add(365, 'days'), // Enable next 1 year
        },
    ];
    let datesBlacklist = [
        {
            start: moment().subtract(1000, 'days'), // Disable past 1000 days
            end: moment().subtract(1, 'days'), // Disable until yesterday
        },
    ];

    useEffect(() => {
        if (viewMode === 'week') {
            const start: any = dayjs(selectedDate).startOf('week');
            const end: any = dayjs(selectedDate).endOf('week');
            setStartDate(start);
            setEndDate(end);
        } else {
            setStartDate(null);
            setEndDate(null);
        }

        memoizedUpdateDateRange(selectedDate);
    }, [selectedDate, viewMode]);

    const renderHeader = useMemo(
        () => (
            <View>
                {isCalendarMinimized ? (
                    <>
                        <CalendarStrip
                            scrollable
                            calendarAnimation={{
                                type: 'sequence',
                                duration: 30,
                            }}
                            daySelectionAnimation={{
                                type: 'background',
                                duration: 200,
                                highlightColor: 'white',
                            }}
                            style={{
                                height: 120,
                                paddingTop: 20,
                                paddingBottom: 10,
                            }}
                            calendarHeaderStyle={{ color: 'white' }}
                            calendarColor={'#8143D1'}
                            dateNumberStyle={{ color: 'white' }}
                            dateNameStyle={{ color: 'white' }}
                            highlightDateNumberStyle={{ color: 'black' }}
                            highlightDateNameStyle={{ color: 'black' }}
                            disabledDateNameStyle={{ color: '#FFFFFFFF' }}
                            disabledDateNumberStyle={{ color: '#FFFFFFFF' }}
                            datesWhitelist={datesWhitelist}
                            // datesBlacklist={datesBlacklist}
                            selectedDate={selectedDate}
                            onDateSelected={handleTopDayDateChange}
                            iconLeft={Asset.LeftArrow}
                            iconRight={Asset.RightArrow}
                            iconContainer={{ flex: 0.1 }}
                        />
                    </>
                ) : (
                    <Calendar
                        current={selectedDate}
                        onDayPress={(day: any) => {
                            setSelectedDate(day.dateString);
                            setIsCalendarMinimized(true);
                        }}
                        monthFormat={'MMMM yyyy'}
                        renderArrow={(direction: any) =>
                            direction === 'left' ? (
                                <ChevronLeft stroke="#000" />
                            ) : (
                                <ChevronRight stroke="#000" />
                            )
                        }
                        markedDates={{
                            [selectedDate]: {
                                selected: true,
                                selectedColor: '#000',
                            },
                        }}
                        theme={{
                            todayTextColor: '#000',
                            selectedDayBackgroundColor: '#000',
                            selectedDayTextColor: '#fff',
                        }}
                    />
                )}
            </View>
        ),
        [
            isCalendarMinimized,
            dateRange,
            renderDateItem,
            selectedDate,
            renderSelectedDate,
            viewMode,
            startDate,
            endDate,
        ]
    );

    const initialScrollOffsetMinutes = useMemo(() => {
        const slots = store?.availableSlots || [];

        const allStartHours: number[] = [];

        slots.forEach((slot: any) => {
            slot.timeSlots?.forEach((timeSlot: any) => {
                if (timeSlot.from) {
                    const hour = parseInt(timeSlot.from.split(':')[0], 10);
                    if (!isNaN(hour)) {
                        allStartHours.push(hour);
                    }
                }
            });
        });

        const earliestHour =
            allStartHours.length > 0 ? Math.min(...allStartHours) : 8;
        return earliestHour * 60;
    }, [store.availableSlots]);

    console.log('store.trainers----------', store.trainers);

    return (
        <>
            <View style={styles.container}>
                <View>
                    <BackButtonHeading
                        Heading="MY SCHEDULE"
                        AppointmentFilter={store.userRole !== UserRole.Trainer}
                        openModal={openFilterModal}
                        facilityName={store.facilityName}
                        handleLocation={handleLocation}
                        setIsCalendarMinimized={setIsCalendarMinimized}
                        isCalendarMinimized={isCalendarMinimized}
                        isCalender={true}
                    />
                </View>

                {store.userRole !== UserRole.Trainer &&
                    store.selectedTrainer?.firstName && (
                        <View style={tw`px-4 bg-primary`}>
                            <Text
                                style={tw`text-white text-center`}
                            >{`${capitalizeFirstLetter(
                                store.selectedTrainer?.firstName
                            )} ${capitalizeFirstLetter(
                                store.selectedTrainer?.lastName
                            )}`}</Text>
                        </View>
                    )}

                {renderHeader}

                {/* {store.userRole !== UserRole.Trainer &&
                    store.trainers?.length > 0 && (
                        <View style={tw`mt-2 px-4`}>
                            <ScrollView
                                horizontal
                                showsHorizontalScrollIndicator={false}
                                style={tw`w-[100%] mb-2`}
                            >
                                {store.trainers?.map(
                                    (trainer: any, index: number) => {
                                        const isSelected =
                                            store.selectedTrainer?.userId ===
                                            trainer.userId;

                                        return (
                                            <TouchableOpacity
                                                key={index}
                                                onPress={() => {
                                                    dispatch(
                                                        SelectTrainer(trainer)
                                                    );
                                                    dispatch(
                                                        GetStaffAvailabilityList(
                                                            {
                                                                selectedDate,
                                                                facilityId:
                                                                    store.facilityId,
                                                            }
                                                        )
                                                    );
                                                }}
                                                style={[
                                                    tw` flex items-center px-3 py-1 rounded-lg`,
                                                    {
                                                        backgroundColor:
                                                            isSelected
                                                                ? '#f0e9ff'
                                                                : '#fff',
                                                        borderColor: isSelected
                                                            ? '#8143D1'
                                                            : '#fff',
                                                        borderWidth: 1,
                                                    },
                                                ]}
                                            >
                                                <View
                                                    style={tw`w-16 h-16 flex justify-center items-center rounded-full bg-primary/80 overflow-hidden mb-2`}
                                                >
                                                    <Text
                                                        style={tw`text-center  text-xl text-[#fff]`}
                                                    >
                                                        {trainer.firstName?.charAt(
                                                            0
                                                        )}
                                                    </Text>
                                                </View>
                                                <Text
                                                    style={tw`text-sm font-bold text-center`}
                                                >
                                                    {trainer.firstName}
                                                </Text>
                                            </TouchableOpacity>
                                        );
                                    }
                                )}
                            </ScrollView>
                        </View>
                    )} */}

                {viewMode === 'list' ||
                viewMode === 'week' ||
                viewMode === 'room' ? (
                    <ScrollView style={tw`p-4 bg-white mb-[18%]`}>
                        {viewMode === 'list' ? (
                            store.userRole === UserRole.Trainer ? (
                                // Trainer: show grouped event list (existing)
                                filteredBookingEvent?.length === 0 ? (
                                    <View style={styles.noEventsContainer}>
                                        <Text style={styles.noEventsText}>
                                            There are no events
                                        </Text>
                                    </View>
                                ) : (
                                    Object.entries(
                                        groupByDate(filteredBookingEvent)
                                    )
                                        .sort(
                                            ([dateA], [dateB]) =>
                                                dayjs(dateB).unix() -
                                                dayjs(dateA).unix()
                                        )
                                        .map(([date, events]: any, index) => (
                                            <View key={index} style={tw`mb-4`}>
                                                <View
                                                    style={tw`flex flex-row items-center`}
                                                >
                                                    <Text
                                                        style={tw`text-lg font-bold text-black mr-2`}
                                                    >
                                                        {dayjs(date).format(
                                                            'D'
                                                        )}
                                                    </Text>
                                                    <Text
                                                        style={tw`text-gray-500 text-lg`}
                                                    >
                                                        {dayjs(date).format(
                                                            'ddd'
                                                        )}
                                                    </Text>
                                                </View>
                                                {events
                                                    .sort(
                                                        (a: any, b: any) =>
                                                            dayjs(
                                                                a.start
                                                            ).unix() -
                                                            dayjs(
                                                                b.start
                                                            ).unix()
                                                    )
                                                    ?.map(
                                                        (
                                                            event: any,
                                                            i: any
                                                        ) => (
                                                            <TouchableOpacity
                                                                key={i}
                                                                onPress={() =>
                                                                    handlePress(
                                                                        event
                                                                    )
                                                                }
                                                                style={[
                                                                    tw`p-3 mt-2 rounded-lg border-l-8`,
                                                                    {
                                                                        borderColor:
                                                                            getCategoryColor(
                                                                                event.classType
                                                                            ),
                                                                        backgroundColor:
                                                                            getCategoryShadowColor(
                                                                                event.classType
                                                                            ),
                                                                    },
                                                                ]}
                                                            >
                                                                <Text
                                                                    style={tw`font-bold text-black`}
                                                                >
                                                                    {
                                                                        event.title
                                                                    }{' '}
                                                                    -{' '}
                                                                    {
                                                                        serviceTypesCaps[
                                                                            event.classType as keyof typeof serviceTypesCaps
                                                                        ]
                                                                    }
                                                                </Text>
                                                                <Text
                                                                    style={tw`text-gray-700`}
                                                                >
                                                                    {formatStartEnd(
                                                                        event.start,
                                                                        event.end
                                                                    )}
                                                                </Text>
                                                            </TouchableOpacity>
                                                        )
                                                    )}
                                            </View>
                                        ))
                                )
                            ) : (
                                <CustomTrainerCalendarList
                                    selectedDate={selectedDate}
                                    trainers={store.trainers}
                                    events={bookingList}
                                    selectedTrainer={store.selectedTrainer}
                                    onTrainerPress={(trainer: any) => {
                                        dispatch(SelectTrainer(trainer));
                                        setViewMode('day');
                                    }}
                                    onEventPress={(event: any) => {
                                        const trainer = store.trainers?.find(
                                            (t: any) =>
                                                t?.userId ===
                                                    event?.trainerId ||
                                                t?.userId ===
                                                    event?.trainer?.userId
                                        );

                                        if (event?.isEmptySlot) {
                                            if (trainer)
                                                dispatch(
                                                    SelectTrainer(trainer)
                                                );
                                            setSelectedDate(
                                                dayjs(event?.start).format(
                                                    'YYYY-MM-DD'
                                                )
                                            );
                                            setViewMode('day');
                                            return;
                                        }

                                        // Handle real event
                                        if (trainer)
                                            dispatch(SelectTrainer(trainer));
                                        setSelectedDate(
                                            dayjs(event?.start).format(
                                                'YYYY-MM-DD'
                                            )
                                        );
                                        setViewMode('day');
                                    }}
                                />
                            )
                        ) : filteredWeekBookingEvents?.length === 0 ? (
                            <View style={styles.noEventsContainer}>
                                <Text style={styles.noEventsText}>
                                    There are no events
                                </Text>
                            </View>
                        ) : (
                            Object.entries(
                                groupByDate(filteredWeekBookingEvents)
                            )
                                .sort(
                                    ([dateA], [dateB]) =>
                                        dayjs(dateB).unix() -
                                        dayjs(dateA).unix()
                                )
                                .map(([date, events]: any, index) => (
                                    <View key={index} style={tw`mb-4`}>
                                        <View
                                            style={tw`flex flex-row items-center`}
                                        >
                                            <Text
                                                style={tw`text-lg font-bold text-black mr-2`}
                                            >
                                                {dayjs(date).format('D')}
                                            </Text>
                                            <Text
                                                style={tw`text-gray-500 text-lg`}
                                            >
                                                {dayjs(date).format('ddd')}
                                            </Text>
                                        </View>
                                        {events
                                            .sort(
                                                (a: any, b: any) =>
                                                    dayjs(a.start).unix() -
                                                    dayjs(b.start).unix()
                                            )
                                            .map((event: any, i: any) => (
                                                <TouchableOpacity
                                                    key={i}
                                                    onPress={() =>
                                                        handlePress(event)
                                                    }
                                                    style={[
                                                        tw`p-3 mt-2 rounded-lg border-l-8`,
                                                        {
                                                            borderColor:
                                                                getCategoryColor(
                                                                    event.classType
                                                                ),
                                                            backgroundColor:
                                                                getCategoryShadowColor(
                                                                    event.classType
                                                                ),
                                                        },
                                                    ]}
                                                >
                                                    <Text
                                                        style={tw`font-bold text-black`}
                                                    >
                                                        {event.title} -{' '}
                                                        {
                                                            serviceTypesCaps[
                                                                event.classType as keyof typeof serviceTypesCaps
                                                            ]
                                                        }
                                                    </Text>
                                                    <Text
                                                        style={tw`text-gray-700`}
                                                    >
                                                        {formatStartEnd(
                                                            event.start,
                                                            event.end
                                                        )}
                                                    </Text>
                                                    <Text
                                                        style={tw`text-gray-700`}
                                                    >
                                                        {event?.roomName}
                                                    </Text>
                                                </TouchableOpacity>
                                            ))}
                                    </View>
                                ))
                        )}
                    </ScrollView>
                ) : (
                    <View>
                        <BigCalendar
                            events={filteredEvents}
                            renderHeader={() => null}
                            height={Dimensions.get('window').height - 160}
                            hourRowHeight={60}
                            renderEvent={customEventRenderer}
                            mode={renderViewMode(viewMode)}
                            ampm={true}
                            swipeEnabled={false}
                            date={selectedDate}
                            onPressEvent={handlePress}
                            onChangeDate={handleDateChange}
                            minHour={0}
                            maxHour={23}
                            scrollOffsetMinutes={initialScrollOffsetMinutes}
                        />
                    </View>
                )}

                <TouchableOpacity
                    onPress={() => {
                        setModalVisible(true);
                        setEvent({});
                    }}
                    style={styles.floatingButton}
                >
                    <PlusIcon stroke="#fff" />
                </TouchableOpacity>
            </View>
            <View style={[styles.footer, tw`py-3`]}>
                {[
                    {
                        key: 'list',
                        label:
                            store.userRole === UserRole.Trainer
                                ? 'List'
                                : 'Staff Schedule',
                    },
                    ...(store.userRole === UserRole.Trainer
                        ? [
                              {
                                  key: 'day',
                                  label: 'Day',
                              },
                          ]
                        : []),
                    {
                        key: 'room',
                        label:
                            store.userRole === UserRole.Trainer
                                ? 'Week'
                                : 'Bookings',
                    },
                ].map(({ key, label }: any) => (
                    <TouchableOpacity
                        key={key}
                        style={tw`   ${
                            viewMode === key ? 'border-b-2 border-primary' : ''
                        }`}
                        onPress={() => setViewMode(key)}
                    >
                        <Text
                            style={tw`${
                                viewMode === key
                                    ? 'text-[#434343] font-semibold pb-0.5 text-center'
                                    : 'text-[#434343]'
                            }`}
                        >
                            {label.charAt(0).toUpperCase() + label.slice(1)}
                        </Text>
                    </TouchableOpacity>
                ))}
            </View>
            <AvailabilityModal
                visible={showModal}
                onClose={closeModal}
                selectedDate={selectedDate}
                {...{ setDeleteCustomRangeModal }}
                role={store.userRole}
            />
            <AvailableSlotModal
                selectedDate={selectedDate}
                visible={showAvailablePopupModal}
                {...{ setShowDeleteModal, setDeleteCustomRangeModal }}
                onClose={() => setAvailablePopupModal(false)}
                event={event}
                role={store.userRole}
            />
            <BookingSlotModal
                selectedDate={selectedDate}
                event={event}
                visible={showBookedPopupModal}
                onClose={() => setBookedPopupModal(false)}
            />

            <FilterModal
                visible={filterModal}
                selectedDate={selectedDate}
                onClose={hideModal}
                onFilterChange={({ roomOrInstructor: r, serviceType: s }) => {
                    if (r) setRoomOrInstructor(r);
                    if (s) setServiceType(s);
                }}
            />

            <UnavailableSlotModal
                selectedDate={selectedDate}
                visible={showUnavailableModal}
                {...{ setShowDeleteModal, setDeleteCustomRangeModal }}
                onClose={() => setUnavailableModalVisible(false)}
                event={event}
            />
            <ConfirmActionDeleteModal
                visible={showDeleteModal}
                onClose={() => setShowDeleteModal(false)}
                onDelete={deleteTimeSlot}
                title={`Are you sure want to delete the time slot from ${formatStartEnd(
                    event.start,
                    event.end,
                    'h:mm A'
                )}`}
            />
            <DeleteCustomRangeModal
                visible={showDeleteCustomRangeModal}
                onClose={() => setDeleteCustomRangeModal(false)}
                onDelete={deleteCustomRangeSlot}
                event={event}
                selectedDate={activeDateForModal}
            />
            <TrainerByOrgModal
                visible={showTrainerListModal}
                onClose={() => setShowTrainerListModal(false)}
                selectedDate={selectedDate}
                facilityId={store.facilityId}
            />
            {/* <CustomTrainerCalendarList
                selectedDate={selectedDate}
                trainers={store.trainers}
                events={bookingList}
            /> */}
        </>
    );
};

export default CalendarScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 10,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    headerText: {
        marginLeft: 10,
        fontSize: 16,
        // fontWeight: 'bold',
        color: '#434343',
    },
    dateList: {
        paddingVertical: 5,
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
    },
    dateItem: {
        paddingHorizontal: 14,
        paddingVertical: 5,
        borderRadius: 20,
        marginHorizontal: 5,
    },
    selectedDateItem: {
        backgroundColor: '#000',
    },
    dateItemText: {
        fontSize: 14,
    },
    selectedDateItemText: {
        color: '#fff',
    },
    expandButton: {
        alignItems: 'center',
        padding: 10,
        backgroundColor: '#f0f0f0',
    },
    schedule: {
        flex: 1,
    },
    footer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        padding: 0,
        borderTopWidth: 1,
        borderTopColor: '#eee',
        backgroundColor: '#f8f8f8',
        position: 'absolute',
        bottom: 0,
        width: '100%',
    },
    footerButton: {
        padding: 10,
    },
    floatingButton: {
        position: 'absolute',
        right: 20,
        bottom: 80,
        backgroundColor: '#0fd99c',
        borderRadius: 30,
        width: 60,
        height: 60,
        justifyContent: 'center',
        alignItems: 'center',
        elevation: 5,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
    },
    dateItemContainer: {
        flexDirection: 'column',
        alignItems: 'center',
    },
    dayItemText: {
        fontSize: 9,
        color: 'gray',
    },
    selectedDayItemText: {
        color: 'white',
    },
    noEventsContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    noEventsText: {
        fontSize: 18,
        color: 'gray',
    },
});
