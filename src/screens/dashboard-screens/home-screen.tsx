import { useEffect } from 'react';
import { <PERSON>ton, Image, ScrollView, Text, View } from 'react-native';
import { useDispatch } from 'react-redux';
import { GetAllFacilitiesByStaffId } from '~/redux/actions/facility-actions';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';

import FacilityProfile from '../facility/facility-profile';

const App = () => {
    const dispatch = useAppDispatch();
    const store = useAppSelector((state) => ({
        userId: state.auth_store.userId,
    }));

    useEffect(() => {
        dispatch(GetAllFacilitiesByStaffId({ userId: store.userId }));
    }, []);

    return (
        <ScrollView>
            <View>
                <FacilityProfile />
            </View>
        </ScrollView>
    );
};

export default App;
