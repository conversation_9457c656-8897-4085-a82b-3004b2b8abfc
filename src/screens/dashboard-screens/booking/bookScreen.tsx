import { useFocusEffect, useRoute } from '@react-navigation/native';
import React, { useCallback, useEffect, useState } from 'react';
import { ScrollView, Text, View } from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import tw from 'twrnc';
import { ClearBookingType } from '~/redux/slices/scheduling-slice';

import { ClassType, UserRole } from '~/constants/enums';

import BackButtonHeading from '~/components/common/back-button-heading';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';

import BookBookingScreen from './book-booking';
import BookAppointmentScreen from './booking-appointment';

// import BookBookingScreen from './BookBookingScreen';
// import BookClassesScreen from './BookClassesScreen';
// import BookCoursesScreen from './BookCoursesScreen';

const bookingOptions = [
    { label: 'Appointment', value: ClassType.PERSONAL_APPOINTMENT },
    { label: 'Booking', value: ClassType.BOOKING },
    // { label: 'Classes', value: 'classes' },
    // { label: 'Courses', value: 'courses' },
];

const getBookingTitle = (type: string, formType: string = '') => {
    const prefix =
        formType === 'edit' ? 'Edit' : formType === 'view' ? 'View' : 'Add';

    switch (type) {
        case ClassType.PERSONAL_APPOINTMENT:
            return `${prefix} Appointment`;
        case ClassType.BOOKING:
            return `${prefix} Booking`;
        case ClassType.CLASSES:
            return `${prefix} Classes`;
        case ClassType.COURSES:
            return `${prefix} Courses`;
        default:
            return `${prefix} Appointment`;
    }
};

const BookScreen = () => {
    const route = useRoute();
    const { scheduleId, type, isDetails, bookClassType }: any =
        route.params || {};
    const dispatch = useAppDispatch();
    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
        bookingType: state.scheduling_store.bookingType,
    }));

    console.log('Store.bookingType ----------', scheduleId, type, isDetails);

    const filteredBookingOptions = bookingOptions.filter(
        (option) =>
            !(
                store.role === UserRole.Trainer &&
                option.value === ClassType.BOOKING
            )
    );

    // Set default selection: If user is Trainer, default to 'appointment'
    const [selectedBookingType, setSelectedBookingType] = useState(
        store.role === UserRole.Trainer
            ? ClassType.PERSONAL_APPOINTMENT
            : ClassType.PERSONAL_APPOINTMENT
    );

    const renderBookingForm = () => {
        switch (selectedBookingType) {
            case ClassType.PERSONAL_APPOINTMENT:
                return <BookAppointmentScreen />;
            case ClassType.BOOKING:
                return <BookBookingScreen />;
            // case 'classes':
            //     return <BookClassesScreen />;
            // case 'courses':
            //     return <BookCoursesScreen />;
            default:
                return <BookAppointmentScreen />;
        }
    };

    useFocusEffect(
        useCallback(() => {
            if (store.bookingType) {
                setSelectedBookingType(store.bookingType);
            }
        }, [store.bookingType])
    );

    return (
        <>
            <BackButtonHeading
                Heading={getBookingTitle(selectedBookingType, type)}
            />
            <ScrollView style={tw`flex-1 `}>
                <View style={tw`flex-1 p-5 bg-white`}>
                    {store.role !== UserRole.Trainer && (
                        <>
                            <Text
                                style={tw`text-[#455560] font-medium text-[13px] pb-2`}
                            >
                                Booking Type
                            </Text>
                            <View
                                style={tw`w-[100%] border-b border-[#45556066] pb-1.5 mb-2`}
                            >
                                <Dropdown
                                    data={filteredBookingOptions}
                                    labelField="label"
                                    valueField="value"
                                    placeholder="Select Booking Type"
                                    value={selectedBookingType}
                                    onChange={(item) =>
                                        setSelectedBookingType(item.value)
                                    }
                                />
                            </View>
                        </>
                    )}

                    {renderBookingForm()}
                </View>
            </ScrollView>
        </>
    );
};

export default BookScreen;
