import {
    useFocusEffect,
    useNavigation,
    useRoute,
} from '@react-navigation/native';
import dayjs from 'dayjs';
import React, { useCallback, useMemo, useState } from 'react';
import { Image, ScrollView, Text, TouchableOpacity, View } from 'react-native';
import DatePicker from 'react-native-date-picker';
import { Dropdown } from 'react-native-element-dropdown';
import tw from 'twrnc';
import {
    PricingByUserAndSubType,
    PricingListingByUserAndType,
    ServiceCategoryListByPackageId,
    roomListingByScheduling,
    roomListingByServiceCategory,
    serviceCategorybyOrganization,
} from '~/redux/actions/booking-action';
import {
    GetClientListing,
    GetClientListingByRole,
} from '~/redux/actions/client-action';
import {
    BookedCalendarData,
    BookedSchedulingDetails,
    CreateBookScheduling,
    UpdateBookedScheduling,
} from '~/redux/actions/scheduling-actions';
import {
    ClearBookingType,
    SetBookingType,
} from '~/redux/slices/scheduling-slice';

import { Asset } from '~/assets';

import { ClassType, UserRole } from '~/constants/enums';

import { Button, TextInput } from '~/components/atoms';
import BackButtonHeading from '~/components/common/back-button-heading';
import CustomCheckbox from '~/components/common/check-box';
import TimePicker from '~/components/common/time-picker';

import { capitalizeFirstLetter } from '~/scripts/function';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useDebounce } from '~/hooks/useDebounce';
import { useLoader } from '~/hooks/useLoader';
import { navigationRef } from '~/hooks/useLocation';

const BookBookingScreen = () => {
    const navigation = useNavigation();
    const route = useRoute();
    const { type, scheduleId, isDetails, bookClassType }: any =
        route.params || {};
    const dispatch = useAppDispatch();
    const [formData, setFormData] = useState<any>({
        location: null,
        client: null,
        trainer: null,
        phone: '',
        email: '',
        package: null,
        remainingSession: null,
        serviceCategory: null,
        room: null,
        startDate: new Date(),
        startTime: '',
        endTime: '',
        duration: null,
        notes: '',
        payLater: false,
        confirmation: false,
    });
    const [errors, setErrors] = useState<any>({});
    const [startDate, setStartDate] = useState(new Date());
    const [location, setLocationState] = useState<string | null>(null);
    const [openStartDatePicker, setOpenStartDatePicker] = useState(false);
    const [durationOptions, setDurationOptions] = useState<any>([]);
    const [loader, startLoader, endLoader] = useLoader();
    const [searchClient, setSearchClient] = useState<string>('');
    const debouncedRequest = useDebounce((callback: any) => callback(), 300);

    const isViewMode: any = type === 'view';
    const isEditMode: any = type === 'edit';

    const store = useAppSelector((state) => ({
        userRole: state.auth_store.role,
        userId: state.auth_store.userId,
        organizationId: state.auth_store.organizationId,
        facilityList: state.facility_store.facilityList || [],
        selectedFacility: state.facility_store.facility?._id,
        selectedTrainer: state.scheduling_store.selectedTrainer,
        classTypes: state.facility_store.classTypes,
        customerList: state.client_store.clientListByStaff,
        customeDetails: state.client_store.clientDetails,
        pricingByUserAndSubType: state.booking_store.pricingByUserAndSubType,
        serviceCategoryByPackage:
            state.booking_store.servicecategoryByPricingList,
        roomList: state.booking_store.roomListByServiceCategory,
        serviceCategoryByOrganization:
            state.booking_store.servicecategoryByOrganization,
        serviceCategoryByOrganizationCount:
            state.booking_store.servicecategoryByOrganizationCount,
    }));

    console.log('Store--------------', store.roomList);

    useFocusEffect(
        useCallback(() => {
            if (store.selectedFacility) {
                setFormData((prev: any) => ({
                    ...prev,
                    location: store.selectedFacility,
                }));
            }
        }, [store.selectedFacility])
    );

    useFocusEffect(
        useCallback(() => {
            if (!scheduleId) {
                dispatch(ClearBookingType());
            }
        }, [scheduleId])
    );

    useFocusEffect(
        useCallback(() => {
            const fetchSchedulingDetails = async () => {
                if (scheduleId) {
                    try {
                        dispatch(BookedSchedulingDetails({ scheduleId })).then(
                            (res: any) => {
                                console.log('Res-------------', res);
                                const scheduleData = res.payload?.data?.data;

                                if (scheduleData) {
                                    const selectedSubType =
                                        scheduleData.subTypeList?.find(
                                            (type: any) =>
                                                type._id ===
                                                scheduleData.subTypeId
                                        );

                                    let newDurationOptions = [];
                                    if (selectedSubType) {
                                        for (let i = 1; i <= 5; i++) {
                                            newDurationOptions.push({
                                                label: `${
                                                    selectedSubType.durationInMinutes *
                                                    i
                                                } min`,
                                                value:
                                                    selectedSubType.durationInMinutes *
                                                    i,
                                            });
                                        }
                                    }
                                    setFormData({
                                        location:
                                            scheduleData.facilityId || null,
                                        client: scheduleData.clientId || null,
                                        phone: scheduleData.clientPhone || '',
                                        email: scheduleData.clientEmail || '',
                                        package:
                                            scheduleData.purchaseId || null,
                                        remainingSession:
                                            scheduleData.remainingSessions ||
                                            '',
                                        serviceCategory:
                                            scheduleData.serviceCategoryId ||
                                            null,
                                        subType: scheduleData.subTypeId || null,
                                        room: scheduleData.roomId || null,
                                        startDate: dayjs(
                                            scheduleData.date
                                        ).toDate(),
                                        startTime: scheduleData.from || '',
                                        endTime: scheduleData.to || '',
                                        duration: scheduleData.duration || '',
                                        notes: scheduleData.notes || '',
                                    });
                                    setStartDate(
                                        dayjs(scheduleData.date).toDate()
                                    );
                                    setDurationOptions(newDurationOptions);
                                    dispatch(
                                        SetBookingType(scheduleData.classType)
                                    );

                                    dispatch(
                                        PricingListingByUserAndType({
                                            userId: scheduleData.clientId,
                                            classType: scheduleData.classType,
                                            staffId: scheduleData.trainerId,
                                        })
                                    );

                                    dispatch(
                                        ServiceCategoryListByPackageId({
                                            packageId: scheduleData.packageId,
                                        })
                                    );

                                    if (
                                        scheduleData.serviceCategoryId &&
                                        scheduleData.facilityId &&
                                        scheduleData.date &&
                                        scheduleData.to &&
                                        serviceCategory
                                    ) {
                                        const reqData = {
                                            serviceId:
                                                scheduleData.serviceCategoryId,
                                            facilityId: scheduleData.facilityId,
                                            classType: ClassType.BOOKING,
                                            date: dayjs(
                                                scheduleData.date
                                            ).format('YYYY-MM-DD'),
                                            startTime: scheduleData.from,
                                            endTime: scheduleData.to,
                                        };

                                        dispatch(
                                            roomListingByScheduling(reqData)
                                        );
                                    }
                                }
                            }
                        );
                    } catch (error) {
                        console.error(
                            'Error fetching scheduling details:',
                            error
                        );
                    } finally {
                        // stopLoader(); Uncomment and implement if you have a loader function
                    }
                }
            };

            fetchSchedulingDetails();
        }, [scheduleId])
    );

    const facilityOptions = store.facilityList?.map((facility: any) => ({
        label: facility.name,
        value: facility._id,
    }));

    const ClientOptions = useMemo(
        () =>
            store.customerList?.map((item: any) => ({
                value: item.userId,
                label: capitalizeFirstLetter(
                    `${item.firstName} ${item.lastName}`
                ),
                id: item._id,
            })),
        [store.customerList]
    );

    const packageOptions =
        store.pricingByUserAndSubType?.map((pkg: any) => ({
            label: pkg.packageName,
            value: pkg._id,
            ...pkg,
        })) || [];

    const serviceCategoryOptions =
        store.serviceCategoryByOrganization?.map((category: any) => ({
            label: category.name,
            value: category._id,
        })) || [];

    const roomOptions = store.roomList?.map((room: any) => ({
        label: room.roomName,
        value: room._id,
    }));

    const subTypeOptions =
        store.serviceCategoryByPackage
            ?.find(
                (category: any) => category._id === formData?.serviceCategory
            )
            ?.appointmentType?.map((subType: any) => ({
                label: subType.name,
                value: subType._id,
            })) || [];

    const transformServiceTypeData = () => {
        return (
            store.serviceCategoryByOrganization?.flatMap((category: any) => [
                {
                    label: category.name,
                    value: category._id,
                    type: 'category',
                    isSelectable: true,
                },
                ...category.appointmentType.map((subType: any) => ({
                    label: `${subType.name} - ${subType.durationInMinutes} min`,
                    value: subType._id,
                    type: 'subType',
                    categoryId: category._id,
                    duration: subType.durationInMinutes,
                    isSelectable: false,
                })),
            ]) || []
        );
    };

    // useFocusEffect(
    //     useCallback(() => {
    //         if (store.customerList && store.customerList?.length > 0) {
    //             const defaultClient = store.customerList?.[0];
    //             setFormData((prev: any) => ({
    //                 ...prev,
    //                 client: defaultClient?.userId,
    //                 phone: defaultClient?.mobile || '',
    //                 email: defaultClient?.email || '',
    //             }));
    //         }
    //     }, [store.customerList])
    // );

    useFocusEffect(
        useCallback(() => {
            if (store.roomList && store.roomList?.length > 0) {
                const defaultRoom = store.roomList?.[0];
                setFormData((prev: any) => ({
                    ...prev,
                    room: defaultRoom?._id,
                }));
            }
        }, [store.roomList])
    );

    const DatePickerComponent = ({
        label,
        date,
        onDateChange,
        open,
        setOpen,
    }: {
        label: any;
        date: Date;
        onDateChange: (date: Date) => void;
        open: boolean;
        setOpen: (open: boolean) => void;
    }) => (
        <View style={tw`w-[45%]`}>
            <Text style={tw`text-black  pb-2`}>{label}</Text>
            <TouchableOpacity
                style={tw`w-[100%] h-[32px] flex justify-center px-1`}
                onPress={() => setOpen(true)}
            >
                <Text style={tw`text-[#455560] text-[14px]`}>
                    {dayjs(date).format('MMM D, YYYY')}
                </Text>
                <DatePicker
                    modal
                    open={open}
                    date={date}
                    onConfirm={(date) => {
                        setOpen(false);
                        onDateChange(date);
                    }}
                    onCancel={() => setOpen(false)}
                    mode="date"
                />
            </TouchableOpacity>
        </View>
    );

    const handleSesrchClient = (search: string) => {
        setSearchClient(search);
    };

    useFocusEffect(
        useCallback(() => {
            const payload = {
                page: 1,
                pageSize: 50,
                facilityId: store.selectedFacility,
                isActive: true,
                ...(searchClient && { search: searchClient }),
            };
            if (searchClient) {
                debouncedRequest(() => {
                    dispatch(GetClientListingByRole(payload));
                });
            } else {
                dispatch(GetClientListingByRole(payload));
            }
        }, [searchClient])
    );

    useFocusEffect(
        useCallback(() => {
            const reqData = {
                // organizationId: store.organizationId,
                classType: ClassType.BOOKING,
                pageSize: 50,
                page: 1,
            };

            dispatch(serviceCategorybyOrganization({ reqData }));
        }, [])
    );

    const { client, subType, serviceCategory } = formData;

    useFocusEffect(
        useCallback(() => {
            if (client && subType) {
                const reqData = {
                    classType: ClassType.BOOKING,
                    clientUserId: client,
                    serviceCategoryId: serviceCategory || '',
                    subTypeId: subType,
                    pageSize: 30,
                    page: 1,
                };

                dispatch(PricingByUserAndSubType(reqData)).then((res: any) => {
                    const packageList = res?.payload?.data?.data;
                    if (packageList && packageList.length > 0) {
                        setFormData((prev: any) => ({
                            ...prev,
                            package: packageList[0]._id,
                            remainingSession:
                                packageList[0].remainingSession || '',
                        }));
                    }
                });
            }
        }, [client, subType, serviceCategory, dispatch])
    );

    useFocusEffect(
        useCallback(() => {
            if (!formData.startTime) {
                const now = dayjs();
                const rounded =
                    now.minute() <= 45
                        ? now
                              .minute(Math.ceil(now.minute() / 15) * 15)
                              .second(0)
                        : now.add(1, 'hour').minute(0).second(0);

                const formatted = rounded.format('HH:mm');

                setFormData((prev: any) => ({
                    ...prev,
                    startTime: formatted,
                }));
            }
        }, [])
    );

    useFocusEffect(
        useCallback(() => {
            const { startTime, endTime, subType, serviceCategory, location } =
                formData;

            if (
                startTime &&
                endTime &&
                startDate &&
                subType &&
                serviceCategory &&
                location
            ) {
                const payload = {
                    classType: ClassType.BOOKING,
                    facilityId: location,
                    serviceId: serviceCategory,
                    date: dayjs(startDate)
                        .startOf('day')
                        .format('YYYY-MM-DDTHH:mm:ss[Z]'),
                    startTime,
                    endTime,
                };

                dispatch(roomListingByScheduling(payload));
            }
        }, [
            formData.startTime,
            formData.endTime,
            formData.subType,
            formData.serviceCategory,
            startDate,
            formData.location,
        ])
    );

    const handleChange = (field: string, value: any) => {
        console.log('Fientld and value-------', field, value);
        setFormData((prev: any) => ({
            ...prev,
            [field]: value,
        }));

        if (errors[field]) {
            setErrors((prevErrors: any) => ({
                ...prevErrors,
                [field]: '',
            }));
        }

        if (field === 'client') {
            const selectedClient: any = store.customerList.find(
                (c: any) => c.userId === value
            );

            const now = dayjs();
            const roundedTime =
                now.minute() <= 45
                    ? now.minute(Math.ceil(now.minute() / 15) * 15).second(0)
                    : now.add(1, 'hour').minute(0).second(0);
            const defaultStartTime = roundedTime.format('HH:mm');

            let updatedForm: any = {
                ...formData,
                client: value,
                phone: selectedClient?.mobile || '',
                email: selectedClient?.email || '',
                remainingSession: null,
                serviceCategory: null,
                subType: null,
                duration: null,
                room: null,
                package: null,
                startTime: defaultStartTime,
                endTime: '',
            };

            // Try to select first available subType
            const categoryList = store.serviceCategoryByOrganization || [];
            const firstCategory = categoryList[0];
            const firstSubType = firstCategory?.appointmentType?.[0];

            if (firstCategory && firstSubType) {
                const durationInMin = firstSubType.durationInMinutes;
                const defaultDuration = durationInMin;
                const endTime = dayjs(
                    `${dayjs(startDate).format(
                        'YYYY-MM-DD'
                    )} ${defaultStartTime}`,
                    'YYYY-MM-DD HH:mm'
                )
                    .add(defaultDuration, 'minute')
                    .format('HH:mm');

                const durationOptions = Array.from({ length: 5 }, (_, i) => ({
                    label: `${durationInMin * (i + 1)} min`,
                    value: durationInMin * (i + 1),
                }));

                updatedForm = {
                    ...updatedForm,
                    subType: firstSubType._id,
                    serviceCategory: firstCategory._id,
                    duration: defaultDuration,
                    endTime,
                };

                setDurationOptions(durationOptions);
            }

            setFormData(updatedForm);
        }

        if (field === 'package') {
            setFormData((prev: any) => ({
                ...prev,
                remainingSession: null,
                // startTime: '',
                // endTime: '',
            }));

            const selectedPackage = store.pricingByUserAndSubType?.find(
                (pkg: any) => pkg._id === value
            );
            if (selectedPackage) {
                setFormData((prev: any) => ({
                    ...prev,
                    remainingSession: selectedPackage.remainingSession || 0,
                }));
            }
        }

        if (field === 'startTime' || field === 'duration') {
            let startTime = field === 'startTime' ? value : formData.startTime;
            let duration = field === 'duration' ? value : formData.duration;

            if (startTime && duration) {
                const formattedStartDate =
                    dayjs(startDate).format('YYYY-MM-DD');

                const startMoment = dayjs(
                    `${formattedStartDate} ${startTime}`,
                    'YYYY-MM-DD HH:mm'
                );

                if (startMoment.isValid()) {
                    const endMoment = startMoment.add(duration, 'minute');
                    const endTime = endMoment.format('HH:mm');

                    setFormData((prev: any) => ({
                        ...prev,
                        startTime: startTime,
                        duration: duration,
                        endTime: endTime,
                    }));
                } else {
                    setFormData((prev: any) => ({
                        ...prev,
                        endTime: '',
                    }));
                }
            }
        }
    };

    const validateForm = () => {
        let newErrors: any = {};
        if (!formData.location) newErrors.location = 'Location is required';
        if (!formData.client) newErrors.client = 'Client is required';
        if (!formData.package) newErrors.package = 'Package is required';
        if (!formData.subType) newErrors.subType = 'Sub type is required';
        if (!formData.serviceCategory)
            newErrors.serviceCategory = 'Service Category is required';
        if (!formData.startTime) newErrors.startTime = 'Start Time is required';
        if (!formData.duration) newErrors.duration = 'Duration is required';
        // if (!startDate) newErrors.duration = 'Start Date is required';

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const resetFields = () => {
        setFormData({
            location: null,
            client: null,
            trainer: null,
            phone: '',
            email: '',
            package: null,
            remainingSession: null,
            serviceCategory: null,
            room: null,
            startDate: new Date(),
            startTime: '',
            endTime: '',
            duration: null,
            notes: '',
            payLater: false,
            confirmation: false,
        });
        setStartDate(new Date());
        setErrors({});
    };

    const handleSubmit = () => {
        if (!validateForm()) return;

        startLoader();
        const payload = {
            facilityId: formData.location,
            // trainerId:
            //     store.userRole !== UserRole.Trainer
            //         ? store.selectedTrainer?.userId
            //         : store.userId,
            clientId: formData.client,
            classType: ClassType.BOOKING,
            ...(formData.room && { roomId: formData.room }),
            ...(formData.notes && { notes: formData.notes }),
            dateRange: 'Single',
            purchaseId: formData.package,
            serviceCategory: formData.serviceCategory,
            sendConfirmation: formData.sendConfirmation || false,
            subType: formData.subType,
            duration: formData.duration,
            date: dayjs(startDate)
                .startOf('day')
                .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
            from: formData.startTime,
            to: formData.endTime,
            ...(!scheduleId && { checkIn: false }),
        };

        console.log('Submitting Payload:', payload);
        if (scheduleId && type) {
            dispatch(
                UpdateBookedScheduling({ payload: { ...payload, scheduleId } })
            )
                .unwrap()
                .then((res: any) => {
                    if (res?.status === 200 || res?.status === 201) {
                        resetFields();
                        navigationRef.goBack();
                        dispatch(
                            BookedCalendarData({
                                facilityId: [formData.location],
                            })
                        );
                    }
                })
                .catch(() => {})
                .finally(endLoader);
        } else {
            dispatch(CreateBookScheduling({ payload }))
                .unwrap()
                .then((res: any) => {
                    // console.log('Res------------', res);
                    if (res?.status === 200 || res?.status === 201) {
                        resetFields();
                        navigationRef.goBack();
                        dispatch(
                            BookedCalendarData({
                                facilityId: [formData.location],
                            })
                        );
                    }
                })
                .catch(() => {})
                .finally(endLoader);
        }
    };

    console.log('form data----------------', formData);

    return (
        <ScrollView style={tw`flex-1 bg-white`}>
            {/* <View style={tw`bg-#D0FF01] w-[100%] h-24 px-4 shadow-md`}>
                <View
                    style={tw`flex justify-between items-center flex-row h-full`}
                >
                    <BackButtonHeading
                        Heading={`${
                            isViewMode ? 'View' : isEditMode ? 'Edit' : 'Book'
                        } Appointment`}
                    />
                    
                </View>
            </View> */}

            <View style={tw`mt-5`}>
                <Text style={tw`text-[#455560] font-medium  text-[13px] pb-2`}>
                    Location
                </Text>
                <View style={tw`w-[100%] border-b border-[#45556066] pb-1.5`}>
                    <Dropdown
                        data={facilityOptions}
                        labelField="label"
                        valueField="value"
                        placeholder="Select Branch"
                        disable={isViewMode}
                        value={formData?.location}
                        selectedTextStyle={{ color: '#455560' }}
                        placeholderStyle={{ color: '#455560' }}
                        itemTextStyle={{ color: '#455560' }}
                        onChange={(item) =>
                            handleChange('location', item.value)
                        }
                        // value={location || store.selectedFacility}
                        // onChange={(item) => setLocationState(item.value)}
                    />
                </View>
                {location ||
                    (store.selectedFacility && (
                        <Text style={tw`text-red-500`}>{errors.location}</Text>
                    ))}
            </View>

            <View style={tw`mt-5`}>
                <Text style={tw`text-[#455560] font-medium  text-[13px] pb-2`}>
                    Client
                </Text>
                <View style={tw`w-[100%] border-b border-[#45556066] pb-1.5`}>
                    <Dropdown
                        data={ClientOptions}
                        labelField="label"
                        valueField="value"
                        disable={isViewMode}
                        placeholder="Select Client..."
                        search
                        searchPlaceholder="Search Client..."
                        value={formData?.client}
                        onChange={(item) => handleChange('client', item.value)}
                        selectedTextStyle={{ color: '#455560' }}
                        placeholderStyle={{ color: '#455560' }}
                        itemTextStyle={{ color: '#455560' }}
                        onChangeText={(text) => {
                            console.log('Text-----dvd-vd-,', text);
                            handleSesrchClient(text);
                        }}
                    />
                </View>
                {errors?.client && (
                    <Text style={tw`text-red-500 text-[11px]`}>
                        {errors?.client}
                    </Text>
                )}
            </View>

            <View
                style={tw`mt-9 border-b border-gray-400 flex flex-row items-center gap-3`}
            >
                <Text style={tw`text-[#455560] font-medium  text-[13px]`}>
                    Phone
                </Text>
                <TextInput
                    value={formData?.phone}
                    editable={false}
                    style={tw``}
                />
            </View>

            <View
                style={tw`mt-9 border-b border-gray-400 flex flex-row items-center gap-3`}
            >
                <Text style={tw`text-[#455560] font-medium  text-[13px]`}>
                    Email
                </Text>
                <TextInput
                    value={formData?.email}
                    editable={false}
                    style={tw` `}
                />
            </View>

            {/* <View style={tw``}>
                <TouchableOpacity
                    style={tw`py-2 flex flex-row items-center gap-2`}
                >
                    <CustomCheckbox
                        value={formData?.confirmation}
                        onValueChange={(item) =>
                            handleChange(
                                'confirmation',
                                !formData?.confirmation
                            )
                        }
                    />
                    <Text style={tw`text-[#455560] text-l`}>
                        Send a confirmation
                    </Text>
                </TouchableOpacity>
            </View> */}

            <View style={tw`mt-9`}>
                <Text style={tw`text-[#455560] font-medium  text-[13px] pb-2`}>
                    Service Type
                </Text>
                <View style={tw`w-[100%] border-b border-[#45556066] pb-1.5`}>
                    <Dropdown
                        data={transformServiceTypeData()}
                        labelField="label"
                        valueField="value"
                        placeholder="Select service category"
                        disable={isViewMode}
                        value={formData?.subType}
                        selectedTextStyle={{ color: '#455560' }}
                        placeholderStyle={{ color: '#455560' }}
                        itemTextStyle={{ color: '#455560' }}
                        // onChange={(item) =>
                        //     handleChange('serviceCategory', item.value)
                        // }
                        renderItem={(item) => (
                            <View
                                style={tw`p-2 ${
                                    item.type === 'category'
                                        ? 'bg-gray-200'
                                        : 'bg-white'
                                }`}
                            >
                                <Text
                                    style={tw`${
                                        item.type === 'category'
                                            ? 'font-bold text-black'
                                            : 'text-gray-700'
                                    }`}
                                >
                                    {item.label}
                                </Text>
                            </View>
                        )}
                        onChange={(item) => {
                            if (item.type === 'category') {
                                setFormData((prev: any) => ({
                                    ...prev,
                                    subType: '',
                                }));
                                return;
                            }

                            if (item.type === 'subType') {
                                const selectedSubTypeDuration = item.duration;
                                const selectedCategoryId = item.categoryId;

                                const generatedDurationOptions = Array.from(
                                    { length: 5 },
                                    (_, i) => ({
                                        label: `${
                                            selectedSubTypeDuration * (i + 1)
                                        } min`,
                                        value:
                                            selectedSubTypeDuration * (i + 1),
                                    })
                                );

                                const defaultDuration =
                                    generatedDurationOptions[0]?.value;

                                setFormData((prev: any) => ({
                                    ...prev,
                                    subType: item.value,
                                    serviceCategory: selectedCategoryId,
                                    duration: defaultDuration,
                                    // startTime: '',
                                    // endTime: '',
                                }));

                                setDurationOptions(generatedDurationOptions);

                                // Dispatch room listing
                                dispatch(
                                    roomListingByServiceCategory({
                                        serviceCategoryId: selectedCategoryId,
                                        facilityId: store.selectedFacility,
                                    })
                                );
                            }
                        }}
                    />
                </View>
                {errors?.subType && (
                    <Text style={tw`text-red-500 text-[11px]`}>
                        {errors?.serviceCategory}
                    </Text>
                )}
            </View>

            <View
                style={tw`mt-9  w-[100%] flex flex-row gap-4 items-end justify-between`}
            >
                <View style={tw` w-[50%] `}>
                    <Text
                        style={tw`text-[#455560] font-medium  text-[13px] pb-2`}
                    >
                        Package
                    </Text>
                    <View style={tw` border-b border-[#45556066] pb-1.5`}>
                        <Dropdown
                            data={packageOptions}
                            labelField="label"
                            valueField="value"
                            placeholder="Select Branch"
                            disable={isViewMode}
                            value={formData?.package}
                            selectedTextStyle={{ color: '#455560' }}
                            placeholderStyle={{ color: '#455560' }}
                            itemTextStyle={{ color: '#455560' }}
                            onChange={(item) =>
                                handleChange('package', item.value)
                            }
                        />
                    </View>
                    {/* {errors?.package && (
                        <Text style={tw`text-red-500`}>{errors?.client}</Text>
                    )} */}
                </View>
                <View style={tw` w-[50%] border-b border-gray-300`}>
                    <TextInput
                        placeholder="Sessions Left"
                        value={formData?.remainingSession?.toString() || 0}
                        editable={false}
                        style={tw``}
                    />
                </View>
                {/* <View style={tw` w-[50%]`}>
                    <TouchableOpacity
                        style={tw`py-2 flex flex-row items-center `}
                    >
                        <CustomCheckbox
                            value={formData?.payLater}
                            onValueChange={(item) =>
                                handleChange('payLater', !formData?.payLater)
                            }
                        />
                        <Text
                            style={tw`text-[#6F6F6F] text-opacity-50 text-[13px] -ml-2`}
                        >
                            Pay Later
                        </Text>
                    </TouchableOpacity>
                </View> */}
            </View>
            {/* <View style={tw`mt-5`}>
                <View style={tw` w-[50%] border-b border-gray-300`}>
                    <TextInput
                        value={formData?.remainingSession?.toString() || ''}
                        editable={false}
                        style={tw``}
                    />
                </View>
            </View> */}

            {/* <View style={tw`mt-5`}>
                <Text style={tw`text-black pb-2`}>SUB-TYPE</Text>
                <View style={tw`w-[100%] border-b border-[#45556066] pb-1.5`}>
                    <Dropdown
                        data={subTypeOptions}
                        labelField="label"
                        valueField="value"
                        placeholder="Select sub type"
                        disable={isViewMode}
                        value={formData?.subType}
                        onChange={(item) => handleChange('subType', item.value)}
                    />
                </View>
                {errors?.subType && (
                    <Text style={tw`text-red-500`}>{errors?.subType}</Text>
                )}
            </View> */}

            <View style={tw`mt-9`}>
                <View
                    style={tw`w-[100%] flex flex-row justify-between items-center border-b border-[#45556066] pb-1.5`}
                >
                    <DatePickerComponent
                        label={
                            <Text
                                style={tw`text-[#455560] font-medium  text-[13px]`}
                            >
                                Start Date
                            </Text>
                        }
                        date={startDate}
                        onDateChange={setStartDate}
                        open={isViewMode ? false : openStartDatePicker}
                        setOpen={setOpenStartDatePicker}
                    />
                    <Image source={Asset.Calender} style={[tw`w-6 h-6`]} />
                </View>
                {errors?.subType && (
                    <Text style={tw`text-red-500 text-[11px]`}>
                        {errors?.subType}
                    </Text>
                )}
            </View>

            <View
                style={tw`mt-5  w-[100%] flex flex-row gap-1 items-end justify-between`}
            >
                <View style={tw`w-[45%]`}>
                    {isViewMode ? (
                        <View style={tw`mt-2 border-b border-gray-300 `}>
                            <Text
                                style={tw`text-[#455560] font-medium mb-2  text-[13px]`}
                            >
                                Start Time
                            </Text>
                            <Text style={tw`text-[#455560] text-[14px] pb-2`}>
                                {formData?.startTime || '--'}
                            </Text>
                        </View>
                    ) : (
                        <TimePicker
                            isOpen
                            placeholder={'Start Time'}
                            title={
                                <Text
                                    style={tw`text-[#455560] font-medium  text-[13px]`}
                                >
                                    Start Time
                                </Text>
                            }
                            value={formData?.startTime}
                            onChange={(value) =>
                                handleChange('startTime', value)
                            }
                        />
                    )}
                    {errors?.startTime && (
                        <Text style={tw`text-red-500 text-[11px]`}>
                            {errors?.startTime}
                        </Text>
                    )}
                </View>
                <View style={tw` w-[45%]`}>
                    <Text
                        style={tw`text-[#455560] font-medium  text-[13px] pb-2`}
                    >
                        Duration
                    </Text>
                    <View style={tw`w-[100%]  pb-1.5 border-b border-gray-400`}>
                        <Dropdown
                            data={durationOptions}
                            labelField="label"
                            valueField="value"
                            placeholder="Select Branch"
                            disable={isViewMode}
                            value={formData?.duration}
                            selectedTextStyle={{ color: '#455560' }}
                            placeholderStyle={{ color: '#455560' }}
                            itemTextStyle={{ color: '#455560' }}
                            onChange={(item) =>
                                handleChange('duration', item.value)
                            }
                        />
                    </View>
                    {errors?.duration && (
                        <Text style={tw`text-red-500 text-[11px]`}>
                            {errors?.duration}
                        </Text>
                    )}
                </View>
            </View>

            <View style={tw`mt-5 flex flex-row gap-3 items-center`}>
                <Text style={tw`text-[#455560] font-medium  text-[13px] pb-2`}>
                    End Time :
                </Text>
                <TextInput
                    value={formData?.endTime || ''}
                    editable={false}
                    style={tw``}
                />
            </View>

            {/* {(store.userRole === UserRole.FRONT_DESK_ADMIN ||
                store.userRole === UserRole.WEB_MASTER) && (
                <View style={tw`mt-5`}>
                    <Text style={tw`text-black pb-2`}>STAFF</Text>
                    <View
                        style={tw`w-[100%] border-b border-[#45556066] pb-1.5`}
                    >
                        <Dropdown
                            data={ClientOptions}
                            labelField="label"
                            valueField="value"
                            disable={isViewMode}
                            placeholder="Select Staff"
                            value={formData?.client}
                            onChange={(item) =>
                                handleChange('client', item.value)
                            }
                        />
                    </View>
                    {errors?.client && (
                        <Text style={tw`text-red-500`}>{errors?.client}</Text>
                    )}
                </View>
            )} */}

            <View style={tw`mt-5`}>
                <Text style={tw`text-[#455560] font-medium  text-[13px]  pb-2`}>
                    Room
                </Text>
                <View style={tw`w-[100%] border-b border-[#45556066] pb-1.5`}>
                    <Dropdown
                        data={roomOptions}
                        labelField="label"
                        valueField="value"
                        placeholder="Select Room"
                        disable={isViewMode}
                        value={formData?.room}
                        selectedTextStyle={{ color: '#455560' }}
                        placeholderStyle={{ color: '#455560' }}
                        itemTextStyle={{ color: '#455560' }}
                        onChange={(item) => handleChange('room', item.value)}
                    />
                </View>
            </View>

            <View
                style={tw`mt-5 ${
                    isViewMode && 'mb-10'
                } border-b border-gray-400`}
            >
                <Text style={tw`text-[#455560] font-medium  text-[13px] pb-2`}>
                    Notes
                </Text>
                <TextInput
                    placeholder="Enter Notes"
                    editable={isViewMode ? false : true}
                    style={tw``}
                    value={formData?.notes}
                    onChangeText={(text) => {
                        handleChange('notes', text);
                    }}
                />
            </View>

            {!isViewMode && (
                <View
                    style={tw`flex justify-center items-center mt-10 mb-[8%]`}
                >
                    <Button
                        // nextImage
                        style={[tw`w-[70%] rounded-full font-bold`]}
                        onPress={handleSubmit}
                    >
                        {isEditMode ? 'Edit' : 'Save'}
                    </Button>
                </View>
            )}
        </ScrollView>
    );
};

export default BookBookingScreen;
