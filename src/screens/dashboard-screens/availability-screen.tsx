import { useFocusEffect, useRoute } from '@react-navigation/native';
import dayjs from 'dayjs';
import { memo, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
    FlatList,
    Modal,
    ScrollView,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import DatePicker from 'react-native-date-picker';
import { Dropdown, MultiSelect } from 'react-native-element-dropdown';
import {
    FacilityDetails,
    getAllClassTypeByStaffId,
    getAllServiceCategories,
} from '~/redux/actions/facility-actions';
// import Modal from 'react-native-modal';
import {
    CreateTrainerAvailability,
    GetStaffAvailabilityDetails,
    GetStaffAvailabilityList,
    UpdateTrainerAvailability,
} from '~/redux/actions/scheduling-actions';
import { SelectTrainer } from '~/redux/slices/scheduling-slice';
import { uniqueId } from '~/utils/utils';

import tw from '~/styles/tailwind';

import CalendarIcon from '~/assets/svg/calendar_Icon.svg';
import TrashIcon from '~/assets/svg/trash_icon.svg';

import { UserRole } from '~/constants/enums';

import { Button, TextInput } from '~/components/atoms';
import BackButtonHeading from '~/components/common/back-button-heading';
import TrainerByOrgModal from '~/components/common/modals/trainers-by-org-modal';
import RadioButtonGroup from '~/components/common/radio-button';
import TimePicker from '~/components/common/time-picker';
import Checkboox from '~/components/library/checkboox';

import {
    capitalizeFirstLetter,
    cleanUpSchedule,
    getRoundedNext30Min,
} from '~/scripts/function';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';
import { navigationRef } from '~/hooks/useLocation';

const PublicDisplayOptions = [
    {
        label: 'Allow client to see Schedule',
        value: 'Allow clients to see schedule',
    },
    {
        label: "Mask staff member's name",
        value: 'Mask staff members name',
    },
    {
        label: 'Hide schedule from clients',
        value: 'Hide schedule from clients',
    },
];

const days = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday',
];

const AvailabilityScreen = () => {
    const route = useRoute();
    const dispatch = useAppDispatch();
    const {
        availabilityType,
        getDetails,
        selectedDate,
        availabilityFormat,
        dateRange,
        from,
        to,
        markUnavailable,
        availableClassType,
        isMarkUnavailable,
    }: any = route.params || {};

    console.log({
        availabilityType,
        getDetails,
        availabilityFormat,
        selectedDate,
        availableClassType,
        isMarkUnavailable,
    });

    // console.log('From to ---------', from, to);

    const [openStartDatePicker, setOpenStartDatePicker] = useState(false);
    const [openEndDatePicker, setOpenEndDatePicker] = useState(false);
    const [showTrainerListModal, setShowTrainerListModal] = useState(false);
    const [selectedRadioValue, setSelectedRadioValue] = useState(
        availabilityFormat || 'Single'
    );
    const [repeatOption, setRepeatOption] = useState<'weekly' | 'custom'>(
        'weekly'
    );

    const initialDayKey = (() => {
        const date = new Date(selectedDate || new Date());
        const dayIndex = date.getDay();
        const dayKeys = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];
        return dayKeys[dayIndex];
    })();
    const [selectedDayKey, setSelectedDayKey] = useState(initialDayKey);
    const [duplicateStatus, setDuplicateStatus] = useState<{
        [day: string]: boolean[];
    }>({});
    const [shiftsByDay, setShiftsByDay] = useState<Record<string, any[]>>({
        mon: [],
        tue: [],
        wed: [],
        thu: [],
        fri: [],
        sat: [],
        sun: [],
    });

    const [modalVisible, setModalVisible] = useState(false);
    const [selectedDays, setSelectedDays] = useState<string[]>([]);
    const [location, setLocationState] = useState<string | null>(null);
    const [publicDisplay, setPublicDisplay] = useState<string | null>(null);
    const [unavailableReason, setUnavailableReason] = useState<string>('');
    const [shifts, setShifts] = useState([
        {
            from: '',
            to: '',
            _id: uniqueId(),
            payRateIds: [],
            selectedServiceCategories: [],
        },
    ]);
    const [errors, setErrors] = useState<any>({
        location: '',
        selectedClassType: '',
        selectedDays: '',
        shifts: [],
        publicDisplay: '',
        unavailableReason: '',
        startDate: '',
        endDate: '',
    });

    const [startDate, setStartDate] = useState(
        new Date(selectedDate) || new Date()
    );
    const [endDate, setEndDate] = useState(
        availabilityFormat === 'Multiple'
            ? new Date(selectedDate)
            : new Date(selectedDate)
    );
    const [selectedClassType, setSelectedClassType] = useState<string | null>(
        null
    );

    const [loader, startLoader, endLoader] = useLoader();
    const isFirstLoad = useRef(true);
    const hasFetchedAvailability = useRef(false);

    const store = useAppSelector((state) => ({
        userRole: state.auth_store.role,
        userId: state.auth_store.userId,
        facilityId: state.facility_store.facility._id,
        facilityList: state.facility_store.facilityList || [],
        selectedFacility: state.facility_store.facility?._id,
        availabilityDetails: state.scheduling_store.availabilityDetails,
        selectedTrainer: state.scheduling_store.selectedTrainer,
        classTypes: state.facility_store.classTypes,
        serviceCategoryStaff: state.facility_store.serviceCategoryList,
        trainers: state.scheduling_store.trainersListByOrganization,
        facilityDetailData: state.facility_store.facilityDetailsData,
    }));

    useFocusEffect(
        useCallback(() => {
            if (store.facilityId) {
                startLoader();
                dispatch(FacilityDetails({ facilityId: store.facilityId }))
                    .unwrap()
                    .then(() => {})
                    .catch(() => {})
                    .finally(endLoader);
            }
        }, [])
    );

    function getFacilityOpeningHoursForDay(dayKey: string) {
        const availability = store.facilityDetailData?.availability?.[0];
        if (!availability) return null;

        const workingHours = availability.workingHours || {};
        const dayWorkingHours = workingHours[dayKey];

        // Return the first time slot or null
        if (Array.isArray(dayWorkingHours) && dayWorkingHours.length > 0) {
            return dayWorkingHours[0];
        }

        return null;
    }

    useFocusEffect(
        useCallback(() => {
            if (!store.facilityDetailData) return;

            const openingHours = getFacilityOpeningHoursForDay(selectedDayKey);

            if (openingHours) {
                // Get default categories and pay rates
                const defaultServiceCategories =
                    store.serviceCategoryStaff?.map(
                        (cat: any) => cat.serviceId
                    ) || [];

                const defaultPayRateIds =
                    store.serviceCategoryStaff?.flatMap(
                        (cat: any) => cat.payRateId || []
                    ) || [];

                setShiftsByDay((prev) => {
                    const currentShifts = prev[selectedDayKey] || [];

                    if (
                        currentShifts.length === 0 ||
                        !currentShifts[0].from ||
                        !currentShifts[0].to
                    ) {
                        const updatedShift = {
                            _id: uniqueId(),
                            from: openingHours.from,
                            to: openingHours.to,
                            selectedServiceCategories: defaultServiceCategories,
                            payRateIds: defaultPayRateIds,
                        };

                        return {
                            ...prev,
                            [selectedDayKey]: [updatedShift],
                        };
                    }
                    return prev;
                });
            }
        }, [
            selectedDayKey,
            store.facilityDetailData,
            store.serviceCategoryStaff,
        ])
    );

    useFocusEffect(
        useCallback(() => {
            if (!getDetails) {
                const shiftDay = new Date(selectedDate || new Date()).getDay();
                const dayKeys = [
                    'sun',
                    'mon',
                    'tue',
                    'wed',
                    'thu',
                    'fri',
                    'sat',
                ];
                const dayKey = dayKeys[shiftDay];

                const openingHours = getFacilityOpeningHoursForDay(dayKey);

                const defaultServiceCategories =
                    store.serviceCategoryStaff?.map(
                        (cat: any) => cat.serviceId
                    ) || [];

                const defaultPayRateIds =
                    store.serviceCategoryStaff?.flatMap(
                        (cat: any) => cat.payRateId || []
                    ) || [];

                const initialShift = {
                    _id: uniqueId(),
                    from: openingHours?.from || '',
                    to: openingHours?.to || '',
                    selectedServiceCategories: defaultServiceCategories,
                    payRateIds: defaultPayRateIds,
                };

                setShiftsByDay((prev) => ({
                    ...prev,
                    [dayKey]: [initialShift],
                }));

                setSelectedDayKey(dayKey);
            }
        }, [getDetails, store.facilityDetailData, store.serviceCategoryStaff])
    );

    function getNext30MinSlot(date = new Date()) {
        const current = new Date(date);
        const minutes = current.getMinutes();
        const roundedMinutes = minutes < 30 ? 30 : 0;
        const nextHour =
            minutes < 30 ? current.getHours() : current.getHours() + 1;

        const start = new Date(current);
        start.setHours(nextHour, roundedMinutes, 0, 0);

        const end = new Date(start);
        end.setMinutes(end.getMinutes() + 30);

        return {
            startTime: start,
            endTime: end,
        };
    }

    useFocusEffect(
        useCallback(() => {
            if (!getDetails) {
                const { startTime, endTime } = getNext30MinSlot();

                const shiftDay = new Date(selectedDate || new Date()).getDay();
                const dayKeys = [
                    'sun',
                    'mon',
                    'tue',
                    'wed',
                    'thu',
                    'fri',
                    'sat',
                ];
                const dayKey = dayKeys[shiftDay];

                const initialShift = {
                    _id: uniqueId(),
                    from: dayjs(startTime).format('HH:mm'),
                    to: dayjs(endTime).format('HH:mm'),
                    selectedServiceCategories: [],
                    payRateIds: [],
                };

                setShiftsByDay((prev) => ({
                    ...prev,
                    [dayKey]: [initialShift],
                }));

                setSelectedDayKey(dayKey);
            }
        }, [getDetails, selectedDate])
    );

    const handleDateSelection = useCallback((value: string) => {
        setSelectedRadioValue(value);
    }, []);

    const handleDaySelect = useCallback((day: string) => {
        setSelectedDays((prevSelectedDays) =>
            prevSelectedDays.includes(day)
                ? prevSelectedDays.filter((selectedDay) => selectedDay !== day)
                : [...prevSelectedDays, day]
        );
    }, []);

    const handleServiceCategoryChange = (index: number, selectedItems: any) => {
        console.log('Selected items:', selectedItems);

        if (!selectedItems || !Array.isArray(selectedItems)) {
            selectedItems = [];
        }

        let selectedCategories = selectedItems?.map((item: any) => item);

        if (selectedCategories.includes('all')) {
            selectedCategories = store.serviceCategoryStaff?.map(
                (option: any) => option.serviceId
            );
        }

        const selectedPayRateIds = selectedCategories?.flatMap(
            (serviceId: any) => {
                const category: any = store.serviceCategoryStaff.find(
                    (service: any) => service.serviceId === serviceId
                );
                return category?.payRateId || [];
            }
        );

        setShiftsByDay((prev) => {
            const updatedDayShifts = [...prev[selectedDayKey]];
            updatedDayShifts[index] = {
                ...updatedDayShifts[index],
                selectedServiceCategories: selectedCategories,
                payRateIds: selectedPayRateIds,
            };
            return {
                ...prev,
                [selectedDayKey]: updatedDayShifts,
            };
        });
    };

    useEffect(() => {
        if (
            !getDetails ||
            !selectedClassType ||
            !startDate ||
            !endDate ||
            availabilityType === 'unavailable' ||
            !store.serviceCategoryStaff.length
        )
            return;

        if (hasFetchedAvailability.current) return;
        hasFetchedAvailability.current = true;

        startLoader();
        const adjustedEndDate = new Date(endDate);
        adjustedEndDate.setHours(23, 59, 59, 999);

        dispatch(
            GetStaffAvailabilityDetails({
                selectedDate: startDate.toISOString(),
                endDate: adjustedEndDate.toISOString(),
                dateRange: availabilityFormat || selectedRadioValue || 'Single',
                classType: selectedClassType,
                from: from || '09:00',
                availabilityStatus: markUnavailable
                    ? 'unavailable'
                    : availabilityType,
            })
        )
            .unwrap()
            .then((res: any) => {
                const timeSlots = res?.res?.data?.data?.timeSlots;
                const newShiftsByDay: Record<string, any[]> = {};

                Object.entries(timeSlots || {}).forEach(
                    ([dayKey, slots]: any) => {
                        newShiftsByDay[dayKey] = (slots || []).map(
                            (slot: any) => {
                                const matchedServiceCategories =
                                    store.serviceCategoryStaff
                                        .filter((cat: any) =>
                                            slot.payRateIds?.some(
                                                (id: string) =>
                                                    cat.payRateId.includes(id)
                                            )
                                        )
                                        .map((cat: any) => cat.serviceId);

                                return {
                                    from: slot.from,
                                    to: slot.to,
                                    _id: slot._id,
                                    selectedServiceCategories:
                                        matchedServiceCategories,
                                    payRateIds: slot.payRateIds,
                                    privacy: slot.privacy,
                                    classType: slot.classType,
                                    availabilityStatus: slot.availabilityStatus,
                                };
                            }
                        );
                    }
                );

                setShiftsByDay((prev) => ({
                    ...prev,
                    ...newShiftsByDay,
                }));
            })
            .finally(() => endLoader());
    }, [selectedClassType, getDetails, startDate, endDate]);

    useFocusEffect(
        useCallback(() => {
            hasFetchedAvailability.current = false;
        }, [])
    );
    const handleEndDateChange = (date: Date) => {
        setEndDate(date);
        hasFetchedAvailability.current = false;
    };

    const handleDuplicateForAllDays = (day: string, index: number) => {
        setDuplicateStatus((prevStatus) => {
            const updated = { ...prevStatus };
            if (!updated[day]) updated[day] = [];

            // Toggle the duplicate status for this slot
            const newStatus = !prevStatus[day]?.[index];
            updated[day][index] = newStatus;

            // Now update shiftsByDay based on newStatus
            setShiftsByDay((prev) => {
                const slotToDuplicate = prev[day]?.[index];
                if (!slotToDuplicate?.from || !slotToDuplicate?.to) return prev;

                const updatedShiftsByDay = { ...prev };

                days.forEach((fullDay) => {
                    const dayKey = fullDay.toLowerCase().slice(0, 3);

                    if (dayKey !== day) {
                        if (newStatus) {
                            // Replace any existing duplicated slot for this source
                            updatedShiftsByDay[dayKey] = [
                                {
                                    ...slotToDuplicate,
                                    _id: uniqueId(),
                                    _duplicatedFrom: `${day}-${index}`,
                                },
                            ];
                        } else {
                            // Remove duplicated slot for this source
                            updatedShiftsByDay[dayKey] = (
                                updatedShiftsByDay[dayKey] || []
                            ).filter(
                                (slot) =>
                                    slot._duplicatedFrom !== `${day}-${index}`
                            );
                        }
                    }
                });

                return updatedShiftsByDay;
            });

            return updated;
        });
    };

    // const handleDuplicateForAllDays = (day: string, index: number) => {
    //     setShiftsByDay((prev) => {
    //         const slotToDuplicate = prev[day]?.[index];
    //         const isSlotValid = slotToDuplicate?.from && slotToDuplicate?.to;

    //         if (!isSlotValid) return prev;

    //         const isChecked = !(duplicateStatus[day]?.[index] || false);
    //         const updatedShiftsByDay = { ...prev };

    //         // Loop through all other days
    //         days.forEach((fullDay) => {
    //             const dayKey = fullDay.toLowerCase().slice(0, 3);

    //             if (dayKey !== day) {
    //                 if (isChecked) {
    //                     // On check: add duplicated shift
    //                     // updatedShiftsByDay[dayKey] = [
    //                     //     ...(updatedShiftsByDay[dayKey] || []),
    //                     //     {
    //                     //         ...slotToDuplicate,
    //                     //         _id: uniqueId(), // give unique id to avoid clash
    //                     //     },
    //                     // ];
    //                     const existingShifts = updatedShiftsByDay[dayKey] || [];

    //                     const isOnlyEmptyShift =
    //                         existingShifts.length === 1 &&
    //                         !existingShifts[0].from &&
    //                         !existingShifts[0].to &&
    //                         (!existingShifts[0].selectedServiceCategories ||
    //                             existingShifts[0].selectedServiceCategories
    //                                 .length === 0);

    //                     // updatedShiftsByDay[dayKey] = isOnlyEmptyShift
    //                     //     ? [
    //                     //           {
    //                     //               ...slotToDuplicate,
    //                     //               _id: uniqueId(),
    //                     //           },
    //                     //       ]
    //                     //     : [
    //                     //           ...existingShifts,
    //                     //           {
    //                     //               ...slotToDuplicate,
    //                     //               _id: uniqueId(),
    //                     //           },
    //                     //       ];
    //                     updatedShiftsByDay[dayKey] = [
    //                         {
    //                             ...slotToDuplicate,
    //                             _id: uniqueId(),
    //                         },
    //                     ];
    //                 } else {
    //                     updatedShiftsByDay[dayKey] = (
    //                         updatedShiftsByDay[dayKey] || []
    //                     ).filter(
    //                         (slot) =>
    //                             !(
    //                                 slot.from === slotToDuplicate.from &&
    //                                 slot.to === slotToDuplicate.to &&
    //                                 JSON.stringify(
    //                                     slot.selectedServiceCategories
    //                                 ) ===
    //                                     JSON.stringify(
    //                                         slotToDuplicate.selectedServiceCategories
    //                                     )
    //                             )
    //                     );
    //                 }
    //             }
    //         });

    //         return updatedShiftsByDay;
    //     });

    //     setDuplicateStatus((prevStatus) => {
    //         const updated = { ...prevStatus };
    //         updated[day] = [...(updated[day] || [])];
    //         updated[day][index] = !(prevStatus[day]?.[index] || false);
    //         return updated;
    //     });
    // };

    const renderSelectedDays = useMemo(() => {
        return selectedDays.length > 0
            ? selectedDays.join(', ')
            : 'Select Days';
    }, [selectedDays]);

    const truncateText = useCallback((text: string, maxLength: number) => {
        return text.length > maxLength
            ? text.slice(0, maxLength) + '...'
            : text;
    }, []);

    const DatePickerComponent = ({
        label,
        date,
        onDateChange,
        open,
        setOpen,
    }: {
        label: string;
        date: Date;
        onDateChange: (date: Date) => void;
        open: boolean;
        setOpen: (open: boolean) => void;
    }) => (
        <View
            style={tw` ${
                selectedRadioValue === 'Multiple' ? 'w-full' : ''
            }    mt-5`}
        >
            <Text style={tw`text-MainTextColor font-medium text-[13px] pb-2`}>
                {label}
            </Text>
            <TouchableOpacity
                style={tw`w-[100%] h-[32px] border-b border-[#45556066] pb-1 flex flex-row justify-between items-center px-1`}
                onPress={() => setOpen(true)}
            >
                <Text style={tw`text-[#455560] text-[14px]`}>
                    {dayjs(date).format('MMM D, YYYY')}
                </Text>
                <DatePicker
                    modal
                    open={open}
                    date={date}
                    onConfirm={(date) => {
                        setOpen(false);
                        onDateChange(date);
                    }}
                    onCancel={() => setOpen(false)}
                    mode="date"
                />
                <CalendarIcon />
            </TouchableOpacity>
        </View>
    );

    const addShiftToDay = (day: string) => {
        setShiftsByDay((prev) => ({
            ...prev,
            [day]: [
                ...(prev[day] || []),
                {
                    _id: uniqueId(),
                    from: '',
                    to: '',
                    selectedServiceCategories: [],
                    payRateIds: [],
                },
            ],
        }));
    };

    const removeShiftFromDay = (day: string, index: number) => {
        if (index === 0) return;
        setShiftsByDay((prev) => ({
            ...prev,
            [day]: prev[day].filter((_, i) => i !== index),
        }));
    };

    const updateDayShift = (
        day: string,
        index: number,
        key: string,
        value: any
    ) => {
        setShiftsByDay((prev) => {
            const dayShifts = [...prev[day]];
            dayShifts[index] = { ...dayShifts[index], [key]: value };
            return { ...prev, [day]: dayShifts };
        });
    };

    function showTrainerModal() {
        setShowTrainerListModal(!showTrainerListModal);
    }

    const facilityOptions = store.facilityList?.map((facility: any) => ({
        label: facility.name,
        value: facility._id,
    }));

    const classTypes = [
        {
            label: 'Personal Appointment',
            value: 'personalAppointment',
        },
        {
            label: 'Classes',
            value: 'classes',
        },
        {
            label: 'Bookings',
            value: 'bookings',
        },
        {
            label: 'Courses',
            value: 'courses',
        },
    ];

    const ClassTypeOption = classTypes?.filter((item: { value: string }) =>
        store.classTypes?.includes(item.value)
    );

    console.log('ClassTypeOption---------------', ClassTypeOption);

    useFocusEffect(
        useCallback(() => {
            if (!getDetails) {
                setSelectedClassType('personalAppointment');
                setPublicDisplay(PublicDisplayOptions[0].value);
            }
        }, [])
    );

    const serviceCategoryOptions = store.serviceCategoryStaff?.map(
        (facility: any) => ({
            label: facility.serviceCategoryName,
            value: facility.serviceId,
        })
    );

    console.log('Triggered shift pre-fill useEffect', {
        getDetails,
        selectedClassType,
    });

    useEffect(() => {
        if (
            !getDetails &&
            availabilityType !== 'unavailable' &&
            selectedClassType &&
            store.serviceCategoryStaff?.length > 0
        ) {
            const allServiceIds = store.serviceCategoryStaff.map(
                (cat: any) => cat.serviceId
            );
            const allPayRateIds = store.serviceCategoryStaff.flatMap(
                (cat: any) => cat.payRateId || []
            );

            const initialShift = {
                _id: uniqueId(),
                from: '',
                to: '',
                selectedServiceCategories: allServiceIds,
                payRateIds: allPayRateIds,
            };

            const dayIndex = new Date(selectedDate || new Date()).getDay();
            const dayKeys = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];
            const dayKey = dayKeys[dayIndex];

            setShiftsByDay((prev) => ({
                ...prev,
                [dayKey]: [initialShift],
            }));
        }
    }, [selectedClassType, store.serviceCategoryStaff, getDetails]);

    useFocusEffect(
        useCallback(() => {
            if (
                store.userRole === UserRole.WEB_MASTER ||
                store.userRole === UserRole.FRONT_DESK_ADMIN
            ) {
                dispatch(
                    getAllClassTypeByStaffId({
                        staffId: store.selectedTrainer?.userId,
                    })
                );
            } else {
                dispatch(getAllClassTypeByStaffId({ staffId: store.userId }));
            }
        }, [store.userId])
    );

    useEffect(() => {
        if (availabilityType !== 'unavailable' && selectedClassType) {
            dispatch(
                getAllServiceCategories({
                    staffId:
                        store.userRole === UserRole.WEB_MASTER ||
                        store.userRole === UserRole.FRONT_DESK_ADMIN
                            ? store.selectedTrainer?.userId
                            : store.userId,
                    data: {
                        serviceType: [selectedClassType],
                        page: 1,
                        pageSize: 50,
                    },
                })
            );
        }
    }, [selectedClassType, availabilityType]);

    const validateFields = () => {
        let newErrors: any = {};
        let isValid = true;

        // if (!location || !store.selectedFacility) {
        //     newErrors.location = 'Location is required';
        //     isValid = false;
        // }

        if (availabilityType !== 'unavailable' && !selectedClassType) {
            newErrors.selectedClassType = 'Class type is required';
            isValid = false;
        }

        if (!publicDisplay && store.userRole !== UserRole.Trainer) {
            newErrors.publicDisplay = 'Select a display option';
            isValid = false;
        }

        // if (availabilityType === 'unavailable' && !unavailableReason) {
        //     newErrors.unavailableReason = 'Reason is required';
        //     isValid = false;
        // }

        if (!startDate) {
            newErrors.startDate = 'Start date is required';
            isValid = false;
        }

        // if (selectedRadioValue === 'Multiple' && !endDate) {
        //     newErrors.endDate = 'End date is required';
        //     isValid = false;
        // }

        // if (selectedRadioValue === 'Multiple' && startDate && endDate) {
        //     if (new Date(startDate) > new Date(endDate)) {
        //         newErrors.endDate = 'End date must be after start date';
        //         isValid = false;
        //     }
        // }

        const shiftErrors: Record<string, any[]> = {};

        // Loop through shiftsByDay
        for (const dayKey in shiftsByDay) {
            shiftErrors[dayKey] = [];
            shiftsByDay[dayKey]?.forEach((shift, index) => {
                let shiftError: any = {};
                if (!shift.from) {
                    shiftError.from = 'Start time is required';
                    isValid = false;
                }
                if (!shift.to) {
                    shiftError.to = 'End time is required';
                    isValid = false;
                }
                if (
                    availabilityType !== 'unavailable' &&
                    (!shift.selectedServiceCategories ||
                        shift.selectedServiceCategories.length === 0)
                ) {
                    shiftError.selectedServiceCategories =
                        'Service Category is required';
                    isValid = false;
                }
                shiftErrors[dayKey][index] = shiftError;
            });
        }

        newErrors.shifts = shiftErrors;
        setErrors(newErrors);
        return isValid;
    };

    function saveDetails() {
        if (!validateFields()) {
            return;
        }
        startLoader();
        let startDateUTC: any = startDate;
        let endDateUTC: any = endDate;

        let selectedKey: string[] = [];

        if (selectedRadioValue === 'Single') {
            const date = new Date(startDate);
            const dayIndex = date.getDay();
            const dayKeys = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];
            const dayKey = dayKeys[dayIndex];

            selectedKey = [dayKey];

            if (
                !Array.isArray(shiftsByDay[dayKey]) ||
                shiftsByDay[dayKey].length === 0
            ) {
                if (
                    selectedDayKey !== dayKey &&
                    Array.isArray(shiftsByDay[selectedDayKey]) &&
                    shiftsByDay[selectedDayKey].length > 0
                ) {
                    shiftsByDay[dayKey] = [...shiftsByDay[selectedDayKey]];
                    shiftsByDay[selectedDayKey] = [];
                }
            }
        }

        const filteredSchedule = Object.fromEntries(
            Object.entries(shiftsByDay).filter(
                ([_, shifts]) => shifts.length > 0
            )
        );

        if (selectedRadioValue === 'Single') {
            const start = new Date(startDate);
            start.setUTCHours(0, 0, 0, 0);

            const end = new Date(startDate);
            end.setUTCHours(23, 59, 59, 999);

            startDateUTC = start.toISOString();
            endDateUTC = end.toISOString();
        } else {
            const start = new Date(startDate);
            start.setUTCHours(0, 0, 0, 0);

            const end = new Date(endDate);
            end.setUTCHours(23, 59, 59, 999);

            startDateUTC = start.toISOString();
            endDateUTC = end.toISOString();
        }

        function manipulateAvailabilityType(type: any) {
            if (type === 'Single') {
                return 'Single';
            } else if (type === 'Multiple') {
                return 'Multiple';
            }
            return 'Single';
        }

        const availabilityDetails: any = {
            dateRange: manipulateAvailabilityType(selectedRadioValue),
            startDate: startDateUTC,
            endDate: endDateUTC,
            availabilityStatus: availabilityType,
            shifts: filteredSchedule,
            schedule: filteredSchedule,
            facilityId: location || store.selectedFacility,
            privacy: publicDisplay,
            reason: unavailableReason,
            markType: repeatOption,
        };

        if (availabilityType !== 'unavailable') {
            availabilityDetails.classType = selectedClassType;
        }

        console.log('Availability Details:', availabilityDetails);

        const isUpdate = availabilityType === 'unavailable' || getDetails;

        if (isUpdate) {
            if (!isMarkUnavailable) {
                availabilityDetails.startTime = from;
            }
            // availabilityDetails.endTime = to;
            dispatch(UpdateTrainerAvailability({ availabilityDetails }))
                .unwrap()
                .then(() => {
                    dispatch(
                        GetStaffAvailabilityList({
                            selectedDate,
                            facilityId: store.facilityId,
                        })
                    ).unwrap();
                    navigationRef.goBack();
                })
                .finally(endLoader);
        } else {
            dispatch(CreateTrainerAvailability({ availabilityDetails }))
                .unwrap()
                .then(() => {
                    dispatch(
                        GetStaffAvailabilityList({
                            selectedDate,
                            facilityId: store.facilityId,
                        })
                    ).unwrap();
                    navigationRef.goBack();
                })
                .finally(endLoader);
        }
    }

    function renderType() {
        return availabilityType === 'unavailable'
            ? 'Unavailability'
            : 'Availability';
    }

    useFocusEffect(
        useCallback(() => {
            if (getDetails) {
                dispatch(
                    GetStaffAvailabilityDetails({
                        selectedDate,
                        dateRange: 'Single',
                        from,
                        to,
                        availabilityStatus: markUnavailable
                            ? 'unavailable'
                            : availabilityType,
                        classType: availableClassType,
                    })
                );
            } else {
                const shiftDay = new Date(selectedDate || new Date()).getDay();
                const dayKeys = [
                    'sun',
                    'mon',
                    'tue',
                    'wed',
                    'thu',
                    'fri',
                    'sat',
                ];
                const dayKey = dayKeys[shiftDay];

                const initialShift = {
                    _id: uniqueId(),
                    from: '',
                    to: '',
                    selectedServiceCategories: [],
                    payRateIds: [],
                };

                setShiftsByDay((prev) => ({
                    ...prev,
                    [dayKey]: [initialShift],
                }));

                setSelectedDayKey(dayKey);
            }
        }, [getDetails])
    );

    useFocusEffect(
        useCallback(() => {
            if (
                store.availabilityDetails &&
                getDetails
                // isFirstLoad.current
            ) {
                console.log(
                    'Availability Details from Store:',
                    store.availabilityDetails
                );

                isFirstLoad.current = false;

                const timeSlotData = store.availabilityDetails?.timeSlot;

                let availableTimeSlots: any[] = [];

                if (Array.isArray(timeSlotData)) {
                    availableTimeSlots = timeSlotData;
                } else if (
                    typeof timeSlotData === 'object' &&
                    timeSlotData !== null
                ) {
                    availableTimeSlots = [timeSlotData];
                }

                if (availableTimeSlots?.length > 0) {
                    const firstSlot = availableTimeSlots[0];

                    console.log('firstSlot---------------', firstSlot);

                    setSelectedClassType(firstSlot.classType);
                    // setSelectedRadioValue(firstSlot.dateRange);
                    setPublicDisplay(firstSlot.privacy);
                    setUnavailableReason(firstSlot.reason);

                    const matchedServiceCategories = store.serviceCategoryStaff
                        .filter((category: any) =>
                            category.payRateId.some((id: string) =>
                                firstSlot.payRateIds.includes(id)
                            )
                        )
                        .map((category: any) => category.serviceId);

                    const shiftDay = new Date(
                        store.availabilityDetails.date
                    ).getDay();
                    const dayKeys = [
                        'sun',
                        'mon',
                        'tue',
                        'wed',
                        'thu',
                        'fri',
                        'sat',
                    ];
                    const dayKey = dayKeys[shiftDay];

                    setShiftsByDay((prev) => ({
                        ...prev,
                        [dayKey]: [
                            {
                                from: firstSlot.from,
                                to: firstSlot.to,
                                _id: firstSlot._id || uniqueId(),
                                selectedServiceCategories:
                                    matchedServiceCategories,
                                payRateIds: firstSlot.payRateIds || [],
                            },
                        ],
                    }));

                    setSelectedDayKey(dayKey);
                }
            }
        }, [store.availabilityDetails, store.serviceCategoryStaff])
    );

    return (
        <ScrollView style={tw`bg-[#fff]`}>
            <BackButtonHeading
                Heading={`${getDetails ? 'Edit ' : 'Add'} ${renderType()}`}
            />
            <View style={tw`py-4 px-5`}>
                {(store.userRole === UserRole.WEB_MASTER ||
                    store.userRole === UserRole.FRONT_DESK_ADMIN) && (
                    <View style={tw`mt-2 `}>
                        <Text
                            style={tw`text-MainTextColor  font-medium text-[14px] pb-2`}
                        >
                            Select Trainer
                        </Text>
                        <View
                            style={tw`border-b w-[100%] border-[#45556066] pb-1.5`}
                        >
                            <Dropdown
                                data={store.trainers?.map((trainer: any) => ({
                                    label: `${capitalizeFirstLetter(
                                        trainer.firstName
                                    )} ${capitalizeFirstLetter(
                                        trainer.lastName
                                    )}`,
                                    value: trainer.userId,
                                }))}
                                labelField="label"
                                valueField="value"
                                placeholder="Select Trainer"
                                value={store.selectedTrainer?.userId}
                                onChange={(item) => {
                                    const selected = store.trainers?.find(
                                        (trainer: any) =>
                                            trainer.userId === item.value
                                    );
                                    if (selected) {
                                        dispatch(SelectTrainer(selected));
                                        dispatch(
                                            getAllClassTypeByStaffId({
                                                staffId: selected?.userId,
                                            })
                                        );
                                    }
                                }}
                            />
                        </View>
                    </View>
                )}

                <View style={tw`mt-5`}>
                    <Text
                        style={tw`text-MainTextColor font-medium text-14 pb-2`}
                    >
                        Location
                    </Text>
                    <View
                        style={tw`w-[100%] border-b border-[#45556066] pb-1.5`}
                    >
                        <Dropdown
                            data={facilityOptions}
                            labelField="label"
                            valueField="value"
                            placeholder="Select Branch"
                            value={location || store.selectedFacility}
                            onChange={(item) => setLocationState(item.value)}
                        />
                    </View>
                    {/* {errors.location && (
                        <Text style={tw`text-red-500 text-sm`}>
                            {errors.location}
                        </Text>
                    )} */}
                </View>

                {availabilityType !== 'unavailable' && (
                    <View style={tw`mt-8`}>
                        <Text
                            style={tw`text-MainTextColor font-medium text-[14px] pb-2`}
                        >
                            Available For
                        </Text>
                        <View
                            style={tw`w-[100%] border-b border-[#45556066] pb-1.5`}
                        >
                            <Dropdown
                                data={ClassTypeOption}
                                labelField="label"
                                valueField="value"
                                placeholder="Select Type"
                                value={selectedClassType}
                                onChange={(item) => {
                                    setSelectedClassType(item.value);
                                }}
                            />
                        </View>
                        {errors.selectedClassType && (
                            <Text style={tw`text-red-500 text-sm`}>
                                {errors.selectedClassType}
                            </Text>
                        )}
                    </View>
                )}

                <View style={tw`bg-[#f2f2f2] py-6 mt-6 px-1 rounded-lg`}>
                    <Text
                        style={tw`text-MainTextColor font-medium text-[14px] `}
                    >
                        Type of {renderType()}
                    </Text>
                    <RadioButtonGroup
                        options={[
                            { label: 'Single-day', value: 'Single' },
                            { label: 'Multi-day', value: 'Multiple' },
                            // { label: 'Ongoing', value: 'Ongoing' },
                        ]}
                        selectedValue={selectedRadioValue}
                        onValueChange={handleDateSelection}
                        style={tw`flex flex-row justify-around mt-4 `}
                    />

                    {selectedRadioValue === 'Single' && (
                        <DatePickerComponent
                            label="Select Date"
                            date={startDate}
                            onDateChange={setStartDate}
                            open={openStartDatePicker}
                            setOpen={setOpenStartDatePicker}
                        />
                    )}

                    {selectedRadioValue === 'Multiple' && (
                        <View style={tw``}>
                            <View style={tw`w-full `}>
                                <DatePickerComponent
                                    label="Start Date"
                                    date={startDate}
                                    onDateChange={setStartDate}
                                    open={openStartDatePicker}
                                    setOpen={setOpenStartDatePicker}
                                />
                                {errors.startDate && (
                                    <Text style={tw`text-red-500 text-sm`}>
                                        {errors.startDate}
                                    </Text>
                                )}

                                {/* <DatePickerComponent
                                    label="End Date"
                                    date={endDate}
                                    onDateChange={setEndDate}
                                    open={openEndDatePicker}
                                    setOpen={setOpenEndDatePicker}
                                /> */}
                                {/* {errors.endDate && (
                                <Text style={tw`text-red-500 text-sm`}>
                                    {errors.endDate}
                                </Text>
                            )} */}
                            </View>
                            <View style={tw`w-full`}>
                                <View style={tw`mt-5`}>
                                    <Text
                                        style={tw`text-MainTextColor font-medium text-[14px] pb-2`}
                                    >
                                        Repeat
                                    </Text>
                                    <View
                                        style={tw`w-full border-b border-[#45556066] pb-1.5`}
                                    >
                                        <Dropdown
                                            data={[
                                                {
                                                    label: 'Repeat Every Week',
                                                    value: 'weekly',
                                                },
                                                {
                                                    label: 'Custom',
                                                    value: 'custom',
                                                },
                                            ]}
                                            labelField="label"
                                            valueField="value"
                                            placeholder="Select Repeat Type"
                                            value={repeatOption}
                                            onChange={(item) =>
                                                setRepeatOption(item.value)
                                            }
                                        />
                                    </View>
                                </View>
                            </View>
                            <View style={tw` w-full`}>
                                {repeatOption === 'custom' && (
                                    <DatePickerComponent
                                        label="End Date"
                                        date={endDate}
                                        onDateChange={handleEndDateChange}
                                        open={openEndDatePicker}
                                        setOpen={setOpenEndDatePicker}
                                    />
                                )}
                            </View>
                            {/* <View style={tw`mt-8`}>
                            <Text
                                style={tw`text-MainTextColor text-[13px] font-medium pb-2`}
                            >
                                Days
                            </Text>
                            <TouchableOpacity
                                style={tw`w-[100%]  border-b border-[#45556066] pb-1 flex justify-center px-1`}
                                onPress={() => setModalVisible(true)}
                            >
                                <Text style={tw`text-[#455560] text-[14px]`}>
                                    {renderSelectedDays}
                                </Text>
                            </TouchableOpacity>
                            {errors.selectedDays && (
                                <Text style={tw`text-red-500 text-sm`}>
                                    {errors.selectedDays}
                                </Text>
                            )}
                        </View> */}
                            <View
                                style={tw`flex flex-col gap-3 justify-around mt-6`}
                            >
                                <Text
                                    style={tw`text-MainTextColor font-medium text-[14px] `}
                                >
                                    Select Days
                                </Text>
                                <View style={tw`flex flex-row justify-around `}>
                                    {days.map((day) => (
                                        <TouchableOpacity
                                            key={day}
                                            onPress={() => {
                                                const key = day
                                                    .toLowerCase()
                                                    .slice(0, 3);

                                                setSelectedDayKey(key);

                                                setShiftsByDay((prev) => {
                                                    if (
                                                        !prev[key] ||
                                                        prev[key].length === 0
                                                    ) {
                                                        return {
                                                            ...prev,
                                                            [key]: [
                                                                {
                                                                    _id: uniqueId(),
                                                                    from: '',
                                                                    to: '',
                                                                    selectedServiceCategories:
                                                                        [],
                                                                    payRateIds:
                                                                        [],
                                                                },
                                                            ],
                                                        };
                                                    }
                                                    return prev;
                                                });
                                            }}
                                            style={tw`w-[10] h-[10] flex justify-center items-center rounded-full ${
                                                selectedDayKey ===
                                                day.toLowerCase().slice(0, 3)
                                                    ? 'bg-primary'
                                                    : 'bg-gray-200'
                                            }`}
                                        >
                                            <Text
                                                style={tw`text-sm ${
                                                    selectedDayKey ===
                                                    day
                                                        .toLowerCase()
                                                        .slice(0, 3)
                                                        ? 'text-white'
                                                        : 'text-black'
                                                }`}
                                            >
                                                {day.slice(0, 3)}
                                            </Text>
                                        </TouchableOpacity>
                                    ))}
                                </View>
                            </View>
                        </View>
                    )}

                    {selectedRadioValue === 'Ongoing' && (
                        <>
                            <View style={tw`flex flex-row justify-between`}>
                                <DatePickerComponent
                                    label="START DATE"
                                    date={startDate}
                                    onDateChange={setStartDate}
                                    open={openStartDatePicker}
                                    setOpen={setOpenStartDatePicker}
                                />
                                <DatePickerComponent
                                    label="END DATE"
                                    date={endDate}
                                    onDateChange={setEndDate}
                                    open={openEndDatePicker}
                                    setOpen={setOpenEndDatePicker}
                                />
                            </View>
                        </>
                    )}

                    {shiftsByDay[selectedDayKey]?.map(
                        (shift: any, index: number) => (
                            <View>
                                <View
                                    key={shift._id}
                                    style={tw`flex flex-row justify-between mt-5 items-start`}
                                >
                                    <View style={tw`w-[45%]`}>
                                        <TimePicker
                                            title={'Start Time'}
                                            placeholder="Start Time"
                                            value={
                                                shiftsByDay[selectedDayKey]?.[
                                                    index
                                                ]?.from || ''
                                            }
                                            onChange={(value) =>
                                                updateDayShift(
                                                    selectedDayKey,
                                                    index,
                                                    'from',
                                                    value
                                                )
                                            }
                                        />
                                        {errors.shifts?.[selectedDayKey]?.[
                                            index
                                        ]?.from && (
                                            <Text
                                                style={tw`text-red-500 text-sm`}
                                            >
                                                {
                                                    errors.shifts[
                                                        selectedDayKey
                                                    ][index].from
                                                }
                                            </Text>
                                        )}
                                    </View>
                                    <View style={tw`w-[45%]`}>
                                        <TimePicker
                                            title={'End Time'}
                                            placeholder="End Time"
                                            value={
                                                shiftsByDay[selectedDayKey]?.[
                                                    index
                                                ]?.to || ''
                                            }
                                            onChange={(value) =>
                                                updateDayShift(
                                                    selectedDayKey,
                                                    index,
                                                    'to',
                                                    value
                                                )
                                            }
                                        />
                                        {errors.shifts?.[selectedDayKey]?.[
                                            index
                                        ]?.to && (
                                            <Text
                                                style={tw`text-red-500 text-sm`}
                                            >
                                                {
                                                    errors.shifts[
                                                        selectedDayKey
                                                    ][index].to
                                                }
                                            </Text>
                                        )}
                                    </View>
                                </View>
                                {index !== 0 && (
                                    <View
                                        style={tw`flex flex-row justify-end pt-2`}
                                    >
                                        <TouchableOpacity
                                            style={tw` py-1 border border-[#8143D1] w-[20%]  rounded-full`}
                                            onPress={() =>
                                                removeShiftFromDay(
                                                    selectedDayKey,
                                                    index
                                                )
                                            }
                                        >
                                            <Text
                                                style={tw`text-center  text-[#8143D1]  text-sm`}
                                            >
                                                Delete
                                            </Text>
                                        </TouchableOpacity>
                                    </View>
                                )}

                                {availabilityType !== 'unavailable' && (
                                    <View style={tw`mt-8`}>
                                        <Text
                                            style={tw`text-MainTextColor text-[14px] font-medium pb-2`}
                                        >
                                            Service
                                        </Text>
                                        <View
                                            style={tw`w-[100%] border-b border-[#45556066] pb-1.5`}
                                        >
                                            <MultiSelect
                                                data={
                                                    serviceCategoryOptions?.length >
                                                    0
                                                        ? [
                                                              {
                                                                  label: 'All',
                                                                  value: 'all',
                                                              },
                                                              ...serviceCategoryOptions,
                                                          ]
                                                        : []
                                                }
                                                // placeholderStyle = {13}
                                                labelField="label"
                                                valueField="value"
                                                placeholder="Select Service Category"
                                                value={
                                                    shiftsByDay[
                                                        selectedDayKey
                                                    ]?.[index]
                                                        ?.selectedServiceCategories ||
                                                    []
                                                }
                                                onChange={(items) => {
                                                    let updatedItems = items;

                                                    // Check if 'all' is selected
                                                    if (items.includes('all')) {
                                                        updatedItems =
                                                            serviceCategoryOptions.map(
                                                                (opt) =>
                                                                    opt.value
                                                            ); // select all real values
                                                    }

                                                    const selectedPayRateIds =
                                                        updatedItems.flatMap(
                                                            (serviceId) => {
                                                                const category =
                                                                    store.serviceCategoryStaff.find(
                                                                        (
                                                                            service
                                                                        ) =>
                                                                            service.serviceId ===
                                                                            serviceId
                                                                    );
                                                                return (
                                                                    category?.payRateId ||
                                                                    []
                                                                );
                                                            }
                                                        );

                                                    // Update shift with selected categories and pay rates
                                                    updateDayShift(
                                                        selectedDayKey,
                                                        index,
                                                        'selectedServiceCategories',
                                                        updatedItems
                                                    );
                                                    updateDayShift(
                                                        selectedDayKey,
                                                        index,
                                                        'payRateIds',
                                                        selectedPayRateIds
                                                    );
                                                }}
                                                selectedStyle={tw`bg-gray-300 border rounded-lg p-1`}
                                            />
                                        </View>
                                        {errors.shifts?.[selectedDayKey]?.[
                                            index
                                        ]?.selectedServiceCategories && (
                                            <Text
                                                style={tw`text-red-500 text-sm`}
                                            >
                                                {
                                                    errors.shifts[
                                                        selectedDayKey
                                                    ][index]
                                                        .selectedServiceCategories
                                                }
                                            </Text>
                                        )}
                                    </View>
                                )}
                                {selectedRadioValue === 'Multiple' && (
                                    <TouchableOpacity
                                        onPress={() =>
                                            handleDuplicateForAllDays(
                                                selectedDayKey,
                                                index
                                            )
                                        }
                                        style={tw`flex-row items-center mt-2`}
                                    >
                                        {/* <Checkboox
                                            checked={
                                                duplicateStatus[
                                                    selectedDayKey
                                                ]?.[index] || false
                                            }
                                            onPress={() =>
                                                handleDuplicateForAllDays(
                                                    selectedDayKey,
                                                    index
                                                )
                                            }
                                            label={
                                                <Text
                                                    style={tw`text-MainTextColor text-[12px] text-opacity-55`}
                                                >
                                                    {' '}
                                                    Duplicate for all days{' '}
                                                </Text>
                                            }
                                            checkColor="#8143D1"
                                            direction="left"
                                        /> */}
                                        <Text
                                            style={tw`text-white bg-primary px-4 py-2 rounded-lg  text-[14px]`}
                                        >
                                            Duplicate for all days
                                        </Text>
                                    </TouchableOpacity>
                                )}
                            </View>
                        )
                    )}
                    {/* <View style={tw`flex flex-row justify-end`}>
                        <TouchableOpacity
                            style={tw`mt-3 mb-7 py-1 border border-[#8143D1] w-[30%]  rounded-full`}
                            onPress={() => addShiftToDay(selectedDayKey)}
                        >
                            <Text
                                style={tw`text-center  text-[#8143D1]  text-sm`}
                            >
                                + Add Shift
                            </Text>
                        </TouchableOpacity>
                    </View> */}
                </View>

                {store.userRole !== UserRole.Trainer && (
                    <View style={tw`mt-5`}>
                        <Text
                            style={tw`text-MainTextColor text-[13px] font-medium pb-2`}
                        >
                            Public Display
                        </Text>
                        <View
                            style={tw`w-[100%] border-b border-[#45556066] pb-1.5`}
                        >
                            <Dropdown
                                data={PublicDisplayOptions}
                                labelField="label"
                                valueField="value"
                                placeholder="Select display options"
                                value={publicDisplay}
                                onChange={(item) =>
                                    setPublicDisplay(item.value)
                                }
                            />
                        </View>
                        {errors.publicDisplay && (
                            <Text style={tw`text-red-500 text-sm`}>
                                {errors.publicDisplay}
                            </Text>
                        )}
                    </View>
                )}
                {availabilityType === 'unavailable' && (
                    <View style={tw`mt-8`}>
                        <Text
                            style={tw`text-MainTextColor font-medium text-14 pb-2`}
                        >
                            Reason for Unavailability
                        </Text>
                        <TextInput
                            style={tw`w-[100%] h-[100px] border border-[#45556066] rounded-md p-2`}
                            multiline
                            maxLength={300}
                            placeholder="Enter reason"
                            value={unavailableReason}
                            onChangeText={setUnavailableReason}
                        />
                        {errors.unavailableReason && (
                            <Text style={tw`text-red-500 text-sm`}>
                                {errors.unavailableReason}
                            </Text>
                        )}
                    </View>
                )}

                <View
                    style={tw`flex justify-center items-center mt-10 mb-[8%] `}
                >
                    <Button
                        loading={loader}
                        onPress={saveDetails}
                        style={[tw`w-[70%] rounded-full font-bold`]}
                    >
                        Save
                    </Button>
                </View>
            </View>
            <Modal
                visible={modalVisible}
                transparent={true} // Keep this as true to apply the overlay effect
                animationType="slide"
                onRequestClose={() => setModalVisible(false)}
            >
                {/* Semi-transparent background overlay */}
                <View
                    style={tw`flex-1 bg-[rgba(0,0,0,0.5)] justify-center items-center`}
                >
                    {/* Modal content */}
                    <View
                        style={tw`w-[80%] bg-white shadow-lg rounded-lg overflow-hidden`}
                    >
                        <View
                            style={tw`bg-#D0FF01] w-full h-16 px-4 shadow-md`}
                        >
                            <View
                                style={tw`flex justify-between items-center flex-row h-full`}
                            >
                                <Text
                                    style={tw`text-black text-base font-semibold uppercase`}
                                >
                                    Select Days
                                </Text>
                                <TouchableOpacity
                                    onPress={() => setModalVisible(false)}
                                >
                                    <Text style={tw`uppercase text-14`}>
                                        Done
                                    </Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                        <View style={tw`p-8`}>
                            <FlatList
                                data={days}
                                keyExtractor={(item) => item}
                                renderItem={({ item }) => (
                                    <TouchableOpacity
                                        style={tw`py-2 flex flex-row items-center justify-between`}
                                        onPress={() => handleDaySelect(item)}
                                    >
                                        <Text
                                            style={tw`text-[#455560] text-lg`}
                                        >
                                            {item}
                                        </Text>
                                        {/* <Checkbox
                                            value={selectedDays.includes(item)}
                                            onValueChange={() =>
                                                handleDaySelect(item)
                                            }
                                        /> */}
                                        <Checkboox
                                            label=""
                                            checked={selectedDays.includes(
                                                item
                                            )}
                                            onPress={() =>
                                                handleDaySelect(item)
                                            }
                                            direction="right"
                                            checkColor="#8143D1"
                                        />
                                    </TouchableOpacity>
                                )}
                            />
                        </View>
                    </View>
                </View>
            </Modal>
            <TrainerByOrgModal
                visible={showTrainerListModal}
                onClose={() => setShowTrainerListModal(false)}
            />
        </ScrollView>
    );
};

export default memo(AvailabilityScreen);
