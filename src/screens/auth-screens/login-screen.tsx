import { Formik } from 'formik';
import React, { useState } from 'react';
import {
    Image,
    ImageBackground,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import * as Yup from 'yup';
import {
    Logout,
    RequestOtpLoginRegistration,
    UserLogin,
} from '~/redux/actions/auth-actions';
import { isMobileEmail } from '~/utils/utils';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import {
    FORGOT_PASSWORD,
    SELECT_ORGANIZATION,
    VERIFICATION_SCREEN,
} from '~/constants/navigation-constant';

import { Button, TextInput } from '~/components/atoms';

import Alertify from '~/scripts/toast';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';

// Validation Schema
const validationSchema = Yup.object().shape({
    contact: Yup.string()
        .required('Email or Mobile is required')
        .test(
            'is-email-or-mobile',
            'Must be a valid email or mobile number',
            (value) => {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                const mobileRegex = /^\d{10}$/; // Adjust regex as needed for your mobile format
                return emailRegex.test(value!) || mobileRegex.test(value!);
            }
        ),
    password: Yup.string().when('isPasswordLogin', {
        is: true,
        then: () => Yup.string().required('Password is required'),
    }),
});

const LoginScreen = ({ navigation }: any) => {
    const [isPasswordLogin, setIsPasswordLogin] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const dispatch = useAppDispatch();
    const [loader, startLoader, endLoader] = useLoader();
    const store = useAppSelector((state) => ({
        selectedOrganization: state.auth_store.selectedOrganization,
    }));

    console.log('store-------------', store.selectedOrganization);

    function handleForgotPassword(values: any) {
        navigation.navigate(FORGOT_PASSWORD, {
            contact: values.contact,
        });
    }

    const handleSubmit = (values: any) => {
        const type = isMobileEmail(values.contact);
        if (!type) return;
        startLoader();

        if (isPasswordLogin) {
            dispatch(
                UserLogin({
                    type,
                    [type]: values.contact,
                    password: values.password,
                })
            )
                .then((res) => {
                    console.log('Role of logged in user -----------', res);
                    if (res?.payload?.res?.data?.data?.user?.role === 'user') {
                        dispatch(Logout());
                        Alertify.error('Login with valid credentials');
                    }
                })
                .finally(endLoader);
        } else {
            dispatch(
                RequestOtpLoginRegistration({ type, [type]: values.contact })
            )
                .unwrap()
                .then((data) => {
                    const otp = data?.res?.data?.data?.otp;
                    navigation.navigate(VERIFICATION_SCREEN, {
                        type,
                        contact: values.contact,
                        otp,
                    });
                })
                .finally(endLoader);
        }
    };

    return (
        <View style={[tw``, styles.container]}>
            <View style={tw`h-full flex-1 justify-center items-center`}>
                <ImageBackground
                    resizeMode="cover"
                    style={[tw`h-100 w-full`, styles.bgImage]}
                    source={Asset.LoginBackground}
                >
                    <View style={tw`absolute px-[5%] bottom-[25%]`}>
                        <Text
                            style={[tw`text-MainTextColor`, styles.headerTitle]}
                        >
                            WELCOME TO{' '}
                            {store.selectedOrganization?.organizationName}
                        </Text>
                    </View>
                    {/* <View style={tw` `}>
                        <Svg
                            style={tw`border-2 border-green-500`}
                            width="100%"
                            height="100%"
                            viewBox="0 0 85 100"
                        >
                            <Polygon
                                points="100,100 0,100 98,75"
                                fill="#F2F2F7" // Adjust the fill color as needed
                            />
                        </Svg>
                    </View> */}
                </ImageBackground>
            </View>
            <View style={tw`h-[45%] px-[5%] pt-2`}>
                <View style={tw`h-full`}>
                    <Text style={tw`text-16 font-bold text-MainTextColor mb-4`}>
                        {isPasswordLogin
                            ? 'Login with Password'
                            : 'Login with OTP'}
                    </Text>
                    <Formik
                        initialValues={{
                            contact: '',
                            password: '',
                            isPasswordLogin: false,
                        }}
                        validationSchema={validationSchema}
                        onSubmit={handleSubmit}
                    >
                        {({
                            handleChange,
                            handleBlur,
                            handleSubmit,
                            values,
                            errors,
                            touched,
                            setFieldValue,
                        }) => (
                            <>
                                <View style={tw``}>
                                    <TextInput
                                        label="Email/Mobile"
                                        placeholder="Email/Mobile"
                                        onChangeText={handleChange('contact')}
                                        onBlur={handleBlur('contact')}
                                        value={values.contact}
                                        style={tw` border-b border-[#4555604D] border-opacity-30 pb-2`}
                                    />
                                    {errors.contact && touched.contact && (
                                        <Text style={styles.error}>
                                            {errors.contact}
                                        </Text>
                                    )}
                                </View>

                                {isPasswordLogin && (
                                    <>
                                        <View style={tw`mt-3`}>
                                            <TextInput
                                                label={
                                                    <Text
                                                        style={tw`text-MainTextColor`}
                                                    >
                                                        Password
                                                    </Text>
                                                }
                                                // style={styles.input}
                                                style={tw`border-b border-[#4555604D] border-opacity-30 pb-2`}
                                                placeholder="Password"
                                                onChangeText={handleChange(
                                                    'password'
                                                )}
                                                onBlur={handleBlur('password')}
                                                value={values.password}
                                                type="password"
                                                // secureTextEntry={!showPassword}
                                            />
                                        </View>
                                        {errors.password &&
                                            touched.password && (
                                                <Text style={styles.error}>
                                                    {errors.password}
                                                </Text>
                                            )}
                                    </>
                                )}

                                <View
                                    style={tw`flex justify-center pt-5 items-center`}
                                >
                                    <Button
                                        style={tw`mt-3 w-[45%] rounded-full text-center`}
                                        onPress={handleSubmit}
                                        nextImage={loader ? false : true}
                                        loading={loader}
                                    >
                                        {isPasswordLogin ? 'Login' : 'Send OTP'}
                                    </Button>
                                </View>
                                <TouchableOpacity
                                    style={[tw``, styles.toggleButton]}
                                    onPress={() => {
                                        setIsPasswordLogin(!isPasswordLogin);
                                        setFieldValue(
                                            'isPasswordLogin',
                                            !isPasswordLogin
                                        );
                                    }}
                                >
                                    <Text
                                        style={[
                                            tw`text-16 text-MainTextColor underline`,
                                        ]}
                                    >
                                        {isPasswordLogin
                                            ? 'Login with OTP'
                                            : 'Login with Password'}
                                    </Text>
                                </TouchableOpacity>

                                <TouchableOpacity
                                    style={[
                                        tw`absolute bottom-3 right-0`,
                                        styles.toggleButton,
                                    ]}
                                    onPress={() =>
                                        navigation.navigate(SELECT_ORGANIZATION)
                                    }
                                >
                                    <Text
                                        style={[tw`text-16 text-MainTextColor`]}
                                    >
                                        {'Change Location'}
                                    </Text>
                                </TouchableOpacity>
                                {/* <TouchableOpacity
                                    style={[
                                        tw`absolute bottom-3 right-0`,
                                        styles.toggleButton,
                                    ]}
                                    onPress={() => {
                                        setIsPasswordLogin(!isPasswordLogin);
                                        setFieldValue(
                                            'isPasswordLogin',
                                            !isPasswordLogin
                                        );
                                    }}
                                >
                                    <Text
                                        style={[tw`text-16 text-MainTextColor`]}
                                    >
                                        {isPasswordLogin
                                            ? 'Login with OTP'
                                            : 'Login with Password'}
                                    </Text>
                                </TouchableOpacity> */}
                                {isPasswordLogin && (
                                    <TouchableOpacity
                                        style={tw`absolute bottom-3 left-0`}
                                        onPress={() =>
                                            handleForgotPassword(values)
                                        }
                                    >
                                        <Text
                                            style={[
                                                tw`text-16 text-MainTextColor`,
                                            ]}
                                        >
                                            Forgot Password ?
                                        </Text>
                                    </TouchableOpacity>
                                )}
                            </>
                        )}
                    </Formik>
                </View>
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
    },
    headerTitle: {
        fontSize: 30,
        fontWeight: 'bold',
        marginBottom: 20,
        textAlign: 'center',
    },
    input: {
        height: 40,
        borderColor: 'gray',
        borderWidth: 1,
        marginBottom: 10,
        paddingHorizontal: 10,
        borderRadius: 5,
    },
    passwordContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderColor: 'gray',
        borderWidth: 1,
        paddingHorizontal: 10,
        borderRadius: 5,
    },
    error: {
        color: 'red',
        marginBottom: 0,
    },
    toggleButton: {
        marginTop: 20,
        alignItems: 'center',
    },
    // toggleButtonText: {
    //     color: '#000000',
    // },
    bgImage: {
        width: '100%',
        height: '100%',
    },
    overlay: {
        transform: [{ rotate: '-15deg' }],
    },
});

export default LoginScreen;
