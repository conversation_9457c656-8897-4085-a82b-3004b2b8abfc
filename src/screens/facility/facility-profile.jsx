import { useFocusEffect, useRoute } from '@react-navigation/native';
import React, { memo, useCallback, useEffect } from 'react';
import { ActivityIndicator, Image, Text, View } from 'react-native';
import { FacilityDetails } from '~/redux/actions/facility-actions';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import BackButtonHeading from '~/components/common/back-button-heading';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';

import AmenityWorkingHour from './amenity-workingHour';
import Detail from './detail';
import FacilitySlider from './facility-slider';

const FacilityProfile = () => {
    const route = useRoute();
    // const { facilityId } = route.params || {};

    const dispatch = useAppDispatch();
    const [loader, startLoader, endLoader] = useLoader();
    const store = useAppSelector((state) => ({
        user: state.auth_store.user,
        facilityId: state.auth_store.facilityId,
        facilityDetailData: state.facility_store.facilityDetailsData,
    }));

    useFocusEffect(
        useCallback(() => {
            if (store.facilityId) {
                startLoader();
                dispatch(FacilityDetails({ facilityId: store.facilityId }))
                    .unwrap()
                    .then(() => {})
                    .catch(() => {})
                    .finally(endLoader);
            }
        }, [])
    );

    return (
        <>
            {loader ? (
                <View style={tw`flex-1 justify-center items-center`}>
                    <ActivityIndicator size="large" color="#8143D1" />
                </View>
            ) : (
                <View style={tw`flex flex-col gap-5 pb-5 bg-[#fafafa]`}>
                    <View style={tw``}>
                        <BackButtonHeading
                            Heading={store.facilityDetailData?.facilityName}
                        />
                    </View>

                    <View style={tw`flex flex-row items-center px-5 gap-4`}>
                        <View style={tw`w-[30%]`}>
                            <Image
                                style={tw`w-24 h-24 rounded-full `}
                                source={{
                                    uri: store.facilityDetailData
                                        ?.profilePicture,
                                }}
                            />
                        </View>
                        <View style={tw`w-[70%] flex flex-col gap-1 pr-5`}>
                            <Text
                                style={tw`text-xl text-MainTextColor font-bold`}
                            >
                                {store.facilityDetailData?.facilityName}
                            </Text>
                            <Text
                                style={tw`text-lg text-MainTextColor leading-5`}
                            >
                                {`${store.facilityDetailData?.cityName?.[0]}, ${store.facilityDetailData?.stateName?.[0]}, ${store.facilityDetailData?.address?.postalCode}`}
                            </Text>
                        </View>
                    </View>
                    {/* -----------------slider----------------------------- */}
                    <View style={tw``}>
                        <FacilitySlider
                            facilityDetails={store.facilityDetailData}
                            gallery={store.facilityDetailData?.gallery}
                        />
                    </View>
                    {/* ----------------------gym details-------------------------- */}

                    <Detail details={store.facilityDetailData} />
                    {/* ----------------------amenity and working hour-------------------------- */}

                    <AmenityWorkingHour details={store.facilityDetailData} />
                </View>
            )}
        </>
    );
};

export default memo(FacilityProfile);
