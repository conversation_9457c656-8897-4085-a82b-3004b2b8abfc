import { useFocusEffect, useNavigation } from '@react-navigation/native';
import dayjs from 'dayjs';
import React, { memo, useCallback, useEffect, useRef, useState } from 'react';
import {
    Animated,
    Image,
    ScrollView,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { GetOrderListing } from '~/redux/actions/order-action';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import { ORDER_DETAILS } from '~/constants/navigation-constant';

import TextInput from '~/components/atoms/text-input';
import BackButtonHeading from '~/components/common/back-button-heading';
import { formatDate } from '~/components/common/function';

import { capitalizeFirstLetter } from '~/scripts/function';

import { useAppDispatch, useAppSelector } from '~/hooks/redux-hooks';
import { useLoader } from '~/hooks/useLoader';

import { ClientSkeleton } from '../staff-profile/client-listing';

export const OrderItemsSkeleton = ({ count = 2 }: { count?: number }) => {
    const opacity = useRef(new Animated.Value(0.6)).current;

    useEffect(() => {
        Animated.loop(
            Animated.sequence([
                Animated.timing(opacity, {
                    toValue: 1,
                    duration: 500,
                    useNativeDriver: true,
                }),
                Animated.timing(opacity, {
                    toValue: 0.6,
                    duration: 500,
                    useNativeDriver: true,
                }),
            ])
        ).start();
    }, []);

    return (
        <View>
            {[...Array(count)].map((_, index) => (
                <View
                    key={index}
                    style={tw`mb-4 p-3 border-t border-[#455560] border-opacity-10`}
                >
                    <Animated.View
                        style={[
                            tw`w-40 h-4 bg-gray-300 rounded mb-2`,
                            { opacity },
                        ]}
                    />
                    <View style={tw`flex-row justify-between`}>
                        <Animated.View
                            style={[
                                tw`w-20 h-3 bg-gray-300 rounded`,
                                { opacity },
                            ]}
                        />
                        <Animated.View
                            style={[
                                tw`w-16 h-3 bg-gray-300 rounded`,
                                { opacity },
                            ]}
                        />
                        <Animated.View
                            style={[
                                tw`w-16 h-3 bg-gray-300 rounded`,
                                { opacity },
                            ]}
                        />
                    </View>
                </View>
            ))}
        </View>
    );
};

const itemsPerPage = 10;

const OrderListing = ({ navigation }: any) => {
    // const navigation: any = useNavigation();
    const dispatch = useAppDispatch();
    const [currentPage, setCurrentPage] = useState(1);
    const [loader, startLoader, endLoader] = useLoader();
    const [orderSearch, setOrderSearch] = useState<string>();

    const store = useAppSelector((state) => ({
        OrderListing: state.order_store.orderList,
        OrderListingCount: state.order_store.orderListCount,
    }));
    const totalPages = Math.ceil(store?.OrderListingCount / itemsPerPage);

    console.log('Store--------', store);

    useFocusEffect(
        useCallback(() => {
            startLoader();
            const payload = {
                page: currentPage,
                pageSize: itemsPerPage,
                ...(orderSearch && { search: orderSearch }),
            };
            dispatch(GetOrderListing(payload))
                .unwrap()
                .then((res: any) => {})
                .finally(endLoader);
        }, [currentPage, orderSearch])
    );

    const handleSearch = (searchText: string) => {
        setOrderSearch(searchText);
        setCurrentPage(1);
    };

    const handleNav = (orderId: string) => {
        console.log('hoohbjh');
        (navigation as any).navigate(ORDER_DETAILS, {
            orderId: orderId,
        });
    };

    const nextPage = () => {
        if (currentPage < totalPages) {
            setCurrentPage(currentPage + 1);
        }
    };

    const prevPage = () => {
        if (currentPage > 1) {
            setCurrentPage(currentPage - 1);
        }
    };

    return (
        <View style={tw`bg-[#FAFAFA]  flex-1`}>
            <ScrollView>
                <BackButtonHeading Heading={'Order Listing'} />

                <View style={tw`px-2 py-4`}>
                    <View style={tw`bg-white p-2 rounded-md shadow-md`}>
                        {/* ----------------search ----------------- */}
                        <View
                            style={tw`bg-[#F7F7F7] rounded-xl my-3 shadow-md py-1 px-2 flex flex-row justify-start items-center`}
                        >
                            <View style={tw`w-[10%]`}>
                                <Image
                                    resizeMode="contain"
                                    style={tw`w-6 h-6`}
                                    source={Asset.SearchIcon}
                                />
                            </View>
                            <View style={tw`w-[75%]`}>
                                <TextInput
                                    placeholder={'Search'}
                                    style={tw` px-2   text-[#3b3b3b]  text-14 font-400`}
                                    value={orderSearch}
                                    onChangeText={handleSearch}
                                />
                            </View>
                        </View>
                        {/* ----------------order list ----------------- */}

                        {loader ? (
                            <OrderItemsSkeleton count={itemsPerPage} />
                        ) : (
                            <View style={tw`px-4 pt-4`}>
                                <Text
                                    style={tw`text-[#455560] text-14 font-semibold`}
                                >
                                    Total Orders : {store?.OrderListingCount}
                                </Text>

                                {store.OrderListing?.map(
                                    (item: any, index: number) => {
                                        return (
                                            <TouchableOpacity
                                                onPress={() =>
                                                    handleNav(item?._id)
                                                }
                                                key={item._id + index}
                                                style={tw`flex flex-row items-center w-full mt-4 border-b border-black/10 pb-2`}
                                            >
                                                {/* <View style={tw`w-[20%]`}>
                                                <Image
                                                    style={tw`h-14 w-14 rounded-full`}
                                                    source={Asset.Avatar}
                                                    resizeMode="cover"
                                                />
                                            </View> */}

                                                <View style={tw`w-[50%] px-2`}>
                                                    <View
                                                        style={tw`flex flex-col gap-1 justify-between `}
                                                    >
                                                        <Text
                                                            style={tw`text-[16px] font-semibold text-[#455560]`}
                                                        >
                                                            {item.userName}
                                                        </Text>

                                                        <Text
                                                            style={tw`text-xs text-[#455560]`}
                                                        >
                                                            {formatDate(
                                                                item.invoiceDate
                                                            )}{' '}
                                                            -{' '}
                                                            {dayjs(
                                                                item.invoiceDate
                                                            ).format('hh:mm A')}
                                                        </Text>
                                                    </View>
                                                </View>
                                                <View
                                                    style={tw`w-[50%] flex flex-row items-center justify-end gap-3`}
                                                >
                                                    <View
                                                        style={tw`flex flex-col gap-1 items-center `}
                                                    >
                                                        <Text
                                                            style={tw`text-[16px] font-semibold text-[#455560]`}
                                                        >
                                                            ₹{item.total}
                                                        </Text>
                                                        <View
                                                            style={tw`flex justify-center ${
                                                                item.paymentStatus ===
                                                                'completed'
                                                                    ? 'bg-[#dcf3de]'
                                                                    : 'bg-[#ECF2FE]'
                                                            } rounded-md px-2 flex-row`}
                                                        >
                                                            <Text
                                                                style={tw`text-sm font-normal ${
                                                                    item.paymentStatus ===
                                                                    'completed'
                                                                        ? 'text-[#11B51F]'
                                                                        : 'text-[#3E79F7]'
                                                                } `}
                                                            >
                                                                {capitalizeFirstLetter(
                                                                    item.paymentStatus
                                                                )}
                                                            </Text>
                                                        </View>
                                                        <View />
                                                    </View>

                                                    <View>
                                                        <Image
                                                            style={tw`w-5 h-5`}
                                                            resizeMode="contain"
                                                            source={
                                                                Asset.RightArrowIcon
                                                            }
                                                        />
                                                    </View>
                                                </View>
                                                {/* <View
                                    style={tw`w-[10%] flex justify-start items-start`}
                                >
                                </View> */}
                                            </TouchableOpacity>
                                        );
                                    }
                                )}
                                {store?.OrderListingCount > 10 && (
                                    <View
                                        style={tw`flex flex-row justify-between items-center my-5`}
                                    >
                                        <TouchableOpacity
                                            onPress={prevPage}
                                            disabled={currentPage === 1}
                                            style={tw`flex flex-row justify-center items-center`}
                                        >
                                            <Image
                                                style={tw`w-7 h-7`}
                                                source={Asset.PreviousIcon}
                                            />
                                            <Text
                                                style={tw`font-semibold leading-none text-black`}
                                            >
                                                Previous
                                            </Text>
                                        </TouchableOpacity>
                                        <Text style={tw`text-black`}>
                                            Page {currentPage} of {totalPages}
                                        </Text>
                                        <TouchableOpacity
                                            onPress={nextPage}
                                            disabled={
                                                currentPage === totalPages
                                            }
                                            style={tw`flex flex-row justify-center items-center`}
                                        >
                                            <Text
                                                style={tw`font-semibold text-black leading-none`}
                                            >
                                                Next
                                            </Text>
                                            <Image
                                                style={tw`w-7 h-7`}
                                                resizeMode="cover"
                                                source={Asset.NextIcon}
                                            />
                                        </TouchableOpacity>
                                    </View>
                                )}
                            </View>
                        )}
                    </View>
                </View>
            </ScrollView>
        </View>
    );
};

export default memo(OrderListing);
