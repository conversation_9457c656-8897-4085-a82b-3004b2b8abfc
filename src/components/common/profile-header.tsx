import { useFocusEffect } from '@react-navigation/native';
import React, { useCallback, useState } from 'react';
import { Image, ImageBackground, Text, View } from 'react-native';
import { useDispatch } from 'react-redux';
import { GetStaffDetails } from '~/redux/actions/client-action';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import { useAppSelector } from '~/hooks/redux-hooks';

function renderProfileImage(img: any, gender: string) {
    if (img) {
        return { uri: img };
    }
    return gender?.toLowerCase() === 'male'
        ? Asset.MaleGenderProfile
        : Asset.FemaleGenderProfile;
}

const ProfileHeader = () => {
    const store = useAppSelector((state) => ({
        firstName: state.auth_store.firstName,
        lastName: state.auth_store.lastName,
        profileImage: state.auth_store.image,
        staffDetails: state.client_store.staffDetails,
    }));

    console.log('first------------ Staff Details', store.staffDetails);
    return (
        <View style={tw`flex justify-center items-center py-7 bg-primary`}>
            <View
                style={tw` w-[100%] flex flex-col gap-3 justify-center items-center `}
            >
                <Image
                    style={tw`w-24 h-24 rounded-full object-cover`}
                    source={renderProfileImage(
                        store.staffDetails?.personalInfo?.profilePicture,
                        store.staffDetails?.personalInfo?.gender
                    )}
                />
            </View>
            <Text style={tw`text-[#ffffff] text-xl font-bold pt-3 capitalize`}>
                {`${store.firstName} ${store.lastName}`}
            </Text>
        </View>
    );
};

export default ProfileHeader;
