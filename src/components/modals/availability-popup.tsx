import React from 'react';
import {
    Animated,
    Image,
    Modal,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';

import tw from '~/styles/tailwind';

import { Asset } from '~/assets';

import { UserRole } from '~/constants/enums';
import {
    AVAILABILITY_SCREEN,
    BOOK_APPOINTMENT,
} from '~/constants/navigation-constant';

import { formatTime } from '~/scripts/function';

import { navigationRef, useLocation } from '~/hooks/useLocation';

interface AvailabilitySlotModalProps {
    visible: boolean;
    onClose: () => void;
    selectedDate?: string;
    setShowDeleteModal?: any;
    setDeleteCustomRangeModal?: any;
    event?: any;
    role?: any;
}

const AvailableSlotModal: React.FC<AvailabilitySlotModalProps> = ({
    visible,
    onClose,
    selectedDate,
    setShowDeleteModal,
    setDeleteCustomRangeModal,
    event,
    role,
}) => {
    const { setLocation } = useLocation();
    const slideAnim = React.useRef(new Animated.Value(300)).current;

    // console.log('Event----- in available slot modal------', event);

    React.useEffect(() => {
        if (visible) {
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 300,
                useNativeDriver: true,
            }).start();
        } else {
            Animated.timing(slideAnim, {
                toValue: 300,
                duration: 300,
                useNativeDriver: true,
            }).start();
        }
    }, [visible]);

    function handleNavigation(type: any, availabilityFormat: any) {
        const from = formatTime(event.start);
        const to = formatTime(event.end);
        const availabilityType = event.unavailable
            ? 'unavailable'
            : 'available';

        console.log('Avalaibity format---------------', event);

        if (availabilityFormat === 'Single') {
            (navigationRef as any).navigate(AVAILABILITY_SCREEN, {
                availabilityType: availabilityType,
                getDetails: true,
                selectedDate,
                availabilityFormat,
                dateRange: 'Single',
                from,
                to,
                availableClassType: event.classType,
            });
        } else if (availabilityFormat === 'Multiple') {
            (navigationRef as any).navigate(AVAILABILITY_SCREEN, {
                availabilityType: availabilityType,
                getDetails: true,
                selectedDate,
                availabilityFormat,
                dateRange: 'Multiple',
                from,
                to,
                availableClassType: event.classType,
            });
        } else if (availabilityFormat === 'unavailable') {
            (navigationRef as any).navigate(AVAILABILITY_SCREEN, {
                availabilityType: 'unavailable',
                selectedDate,
                markUnavailable: false,
                isMarkUnavailable: true,
                getDetails: true,
                availabilityFormat: 'Single',
                dateRange: 'Single',
                from,
                to,
                availableClassType: event.classType,
            });
        }
    }

    return (
        <Modal
            transparent={true}
            visible={visible}
            animationType="fade"
            onRequestClose={onClose}
        >
            <View style={styles.overlay}>
                <TouchableOpacity onPress={onClose} />
                <View
                    style={[
                        styles.modalContainer,
                        // { transform: [{ translateY: slideAnim }] },
                    ]}
                >
                    <TouchableOpacity
                        style={styles.closeButton}
                        onPress={onClose}
                    >
                        {/* <Text style={styles.closeButtonText}>×</Text> */}
                        <Image
                            style={tw` w-4 h-4`}
                            source={Asset.CloseIcon}
                            resizeMode="contain"
                        />
                    </TouchableOpacity>
                    {role !== UserRole.Trainer && (
                        <TouchableOpacity
                            style={styles.optionButton}
                            onPress={() =>
                                handleNavigation('availability', 'Single')
                            }
                        >
                            <Text style={styles.optionText}>
                                Edit this shift
                            </Text>
                        </TouchableOpacity>
                    )}
                    <TouchableOpacity
                        style={styles.optionButton}
                        onPress={() =>
                            (navigationRef as any).navigate(BOOK_APPOINTMENT)
                        }
                    >
                        <Text style={styles.optionText}>Book Appointment</Text>
                    </TouchableOpacity>

                    {/* <TouchableOpacity
                        style={styles.optionButton}
                        onPress={() =>
                            handleNavigation('availability', 'Multiple')
                        }
                    >
                        <Text style={styles.optionText}>
                            Edit Multiple Availability
                        </Text>
                    </TouchableOpacity> */}
                    {/* <TouchableOpacity
                        style={styles.optionButton}
                        onPress={() => {
                            onClose();
                            setShowDeleteModal(true);
                        }}
                    >
                        <Text style={styles.optionText}>
                            Clear single shift
                        </Text>
                    </TouchableOpacity> */}
                    {/* <TouchableOpacity
                        style={styles.optionButton}
                        onPress={() => {
                            onClose();
                            setDeleteCustomRangeModal(true);
                        }}
                    >
                        <Text style={styles.optionText}>Clear all shifts</Text>
                    </TouchableOpacity> */}
                    <TouchableOpacity
                        style={styles.optionButton}
                        onPress={() =>
                            handleNavigation('availability', 'unavailable')
                        }
                    >
                        <Text style={styles.optionText}>Mark Unavailable</Text>
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.optionButton}>
                        <Text style={styles.optionText}>Scan For Booking</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    overlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'flex-end',
        alignItems: 'flex-end',
    },
    modalContainer: {
        backgroundColor: 'white',
        padding: 20,
        borderRadius: 20,
        width: '100%',
        alignItems: 'center',
    },
    closeButton: {
        position: 'absolute',
        top: 10,
        right: 10,
        zIndex: 1,
        padding: 2,
    },
    closeButtonText: {
        fontSize: 24,
        color: '#455560',
    },
    optionButton: {
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row',
        borderBottomColor: '#DEDEDE',
        borderBottomWidth: 1,
        paddingVertical: 15,
        marginHorizontal: 5,
        width: '100%',
    },
    optionText: {
        color: '#455560',
    },
});

export default AvailableSlotModal;
