export const Asset = {
    ResetIcon: require('~/assets/images/reset-icon.png'),
    EyeIcon: require('~/assets/images/eye-icon.png'),
    ContactIcon: require('~/assets/images/contact-icon.png'),
    ReviewsIcon: require('~/assets/images/reviews-icon.png'),
    InfoOutlinedIcon: require('~/assets/images/info-outlined.png'),
    GroupIcon: require('~/assets/images/group.png'),
    TagIcon: require('~/assets/images/tag-icon.png'),
    PLusImage: require('~/assets/images/plusImg.png'),
    Tick: require('~/assets/images/tick.png'),
    Avatar: require('~/assets/images/Avatar.png'),
    MinusImage: require('~/assets/images/minusImg.png'),
    ArrowImage: require('~/assets/images/chevron-right.png'),
    LoseWeightIcon: require('~/assets/images/Lose_Weight_gray.png'),
    GainWeightIcon: require('~/assets/images/Gain_weight.png'),
    BodyBuildingIcon: require('~/assets/images/Bodybuilding.png'),
    StrengthIcon: require('~/assets/images/Strength.png'),
    Stamina_MobilityIcon: require('~/assets/images/Staminaandmobility.png'),
    InjuryRehabilitationIcon: require('~/assets/images/Injuryrehabilitation.png'),
    HomeIcon: require('~/assets/images/Home.png'),
    HomeWhiteIcon: require('~/assets/images/home-white.png'),
    BookIcon: require('~/assets/images/Book.png'),
    BookWhiteIcon: require('~/assets/images/book-white.png'),
    ProfileIcon: require('~/assets/images/Profile.png'),
    ProfileWhite: require('~/assets/images/profile-white.png'),
    MoreIcon: require('~/assets/images/More.png'),
    MoreWhiteIcon: require('~/assets/images/more-white.png'),
    ScheduleWhiteIcon: require('~/assets/images/schedule-white.png'),

    LoginBackground: require('~/assets/images/login_bg.png'),
    CircleBackButton: require('~/assets/images/Circle_Left.png'),
    BackButton: require('~/assets/images/back-icon.png'),
    MinusIcon: require('~/assets/images/Minus_icon.png'),

    Sign_upBg: require('~/assets/images/SIgn_upBg.png'),
    ProfilePic: require('~/assets/images/ProfilePic.png'),
    SettingIcon: require('~/assets/images/Setting_icon.png'),
    SubscriptionIcon: require('~/assets/images/Subscription_icon.png'),
    LogoutIcon: require('~/assets/images/logout_icon.png'),

    CameraIcon: require('~/assets/images/camera.png'),
    GalleryIcon: require('~/assets/images/gallery.png'),
    ProfileImg: require('~/assets/images/profileImg.png'),
    ProfileBgImg: require('~/assets/images/bg-image.png'),
    DownArrow: require('~/assets/images/up_arrow.png'),
    UpArrows: require('~/assets/images/down_arrow.png'),
    LocationIcon: require('~/assets/images/location_icon.png'),
    CloseIcon: require('~/assets/images/Close_icon.png'),

    MapIcon: require('~/assets/images/map_icon.png'),
    CheckboxUnchecked: require('~/assets/images/Checkbox_unchecked.png'),
    CheckboxChecked: require('~/assets/images/Checkbox_checked.png'),
    ClientIcon: require('~/assets/images/icons/client_icon.png'),
    AttendanceIcon: require('~/assets/images/icons/attrndance_icon.png'),
    NotificationIcon: require('~/assets/images/icons/notification_icon.png'),
    Edit: require('~/assets/images/icons/edit.png'),
    Setting: require('~/assets/images/icons/setting.png'),
    PrivacyIcon: require('~/assets/images/icons/privacy_Icon.png'),
    LogOut: require('~/assets/images/icons/log_out.png'),
    FilterIcon: require('~/assets/images/icons/filter_icon.png'),
    FilterWhiteIcon: require('~/assets/images/icons/filter-white.png'),
    SearchIcon: require('~/assets/images/icons/search_icon.png'),
    BlackSearchIcon: require('~/assets/images/Black_Search.png'),
    RightArrowIcon: require('~/assets/images/icons/chevron-right.png'),
    NextIcon: require('~/assets/images/icons/next_icon.png'),
    PreviousIcon: require('~/assets/images/icons/previous_icon.png'),
    ProfilePicture: require('~/assets/images/profile_img1.png'),
    ProfilePicture2: require('~/assets/images/profile_img2.png'),
    ProfilePicture3: require('~/assets/images/profile_img3.png'),
    ProfilePicture4: require('~/assets/images/profile_img4.png'),
    ProfileIconNew: require('~/assets/images/profile-icon.png'),
    GeneralIcon: require('~/assets/images/icons/gernal_icon.png'),
    InfoIcon: require('~/assets/images/icons/info_icon.png'),
    NotesIcon: require('~/assets/images/icons/notes_icon.png'),
    PolicyIcon: require('~/assets/images/icons/policy_icon.png'),
    DOBIcon: require('~/assets/images/icons/dob_icon.png'),
    GenderIcon: require('~/assets/images/icons/gender_icon.png'),
    ActivityIcon: require('~/assets/images/icons/activity_icon.png'),
    PhoneIcon: require('~/assets/images/icons/phone_icon.png'),
    MailIcon: require('~/assets/images/icons/mail_icon.png'),
    AddressIcon: require('~/assets/images/icons/address_icon.png'),
    CheckCircleIcon: require('~/assets/images/icons/Check_circle_icon.png'),
    UncheckCircleIcon: require('~/assets/images/icons/Uncheck_circle_icon.png'),
    PasswordImg: require('~/assets/images/Password.png'),
    Weight: require('~/assets/images/icons/weight.png'),
    Height: require('~/assets/images/icons/height.png'),
    Goal: require('~/assets/images/icons/goal.png'),
    Chest: require('~/assets/images/icons/chest.png'),
    Shoulder: require('~/assets/images/icons/shoulder.png'),
    Arm: require('~/assets/images/icons/arm.png'),
    Bicep: require('~/assets/images/icons/bicep.png'),
    Forearm: require('~/assets/images/icons/forearm.png'),
    Wrist: require('~/assets/images/icons/wrist.png'),
    Hip: require('~/assets/images/icons/hip.png'),
    Thigh: require('~/assets/images/icons/thigh.png'),
    Calf: require('~/assets/images/icons/calf.png'),
    Calender: require('~/assets/images/icons/calender-icon.png'),
    RightArrow: require('~/assets/images/arrow-right-2.png'),
    LeftArrow: require('~/assets/images/arrow-left.png'),
    CalenderWhite: require('~/assets/images/calender-white.png'),
    SelectOrgHop: require('~/assets/images/select-org-hop.png'),
    HomeIconFooter: require('~/assets/images/Home_fill.png'),
    MoreIconFooter: require('~/assets/images/More_fill.png'),
    MaleGenderProfile: require('~/assets/images/Male_profile_placeholder.png'),
    FemaleGenderProfile: require('~/assets/images/Female_profile_placeholder.png'),
};
