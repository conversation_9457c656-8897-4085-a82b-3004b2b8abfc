import { createAsyncThunk } from '@reduxjs/toolkit';

import {
    COURSE_PACKAGE_LIST,
    PRICE_LIST_BY_USER_AND_TYPE,
    PRICING_BY_USER_AND_SUBTYPE,
    ROOM_LIST_BY_SCHEDULING,
    ROOM_LIST_BY_SERVICE_CATEGORY,
    SERVICE_CATEGORY_LIST,
    SERVICE_CATEGORY_LIST_BY_ORGANIZATION,
    SERVICE_CATEGORY_LIST_PACKAGE_ID,
} from '~/constants/api-constant';

import { getApi, postApi } from '~/scripts/api-services';
import Alertify from '~/scripts/toast';

interface ServiceCategoryParams {
    page?: number;
    pageSize?: number;
    search?: string;
    classType?: string;
    packageId?: string;
    organizationId?: string;
    facilityIds?: string[];
    trainerIds?: string[];
}

/*------------------- Service Category List ------------------ */
export const ServiceCategoryList: any = createAsyncThunk(
    'ServiceCategoryList',
    async ({ page, pageSize, search, classType }: ServiceCategoryParams) => {
        try {
            const response = await postApi(SERVICE_CATEGORY_LIST, {
                page,
                pageSize,
                search,
                classType,
            });
            return response;
        } catch (error: any) {
            console.log('Error fetch Services List API ', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

export const PricingListingByUserAndType: any = createAsyncThunk(
    'PricingListingByUserAndType',
    async (reqData: any) => {
        try {
            const response = await postApi(
                PRICE_LIST_BY_USER_AND_TYPE,
                reqData
            );
            return response;
        } catch (error: any) {
            console.log('Error', error);
        }
    }
);

/*------------------- Service Category List by Package Id ------------------ */

export const ServiceCategoryListByPackageId: any = createAsyncThunk(
    'ServiceCategoryListByPackageId',
    async ({ packageId }: ServiceCategoryParams) => {
        try {
            const response = await getApi(
                `${SERVICE_CATEGORY_LIST_PACKAGE_ID}/${packageId}`
            );
            return response;
        } catch (error: any) {
            console.log('Error fetch Services List API ', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Room List by Service Category ------------------ */

export const roomListingByServiceCategory: any = createAsyncThunk(
    'roomListingByServiceCategory',
    async (reqData) => {
        try {
            const response = await postApi(
                ROOM_LIST_BY_SERVICE_CATEGORY,
                reqData
            );
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Service Category by organization ------------------ */

export const serviceCategorybyOrganization: any = createAsyncThunk(
    'serviceCategorybyOrganization',
    async ({ reqData }: any, { dispatch, getState, rejectWithValue }: any) => {
        try {
            let { organizationId, selectedOrganization } =
                getState().auth_store;
            const orgId =
                selectedOrganization?.organizationId || organizationId;
            const response = await postApi(
                SERVICE_CATEGORY_LIST_BY_ORGANIZATION,
                { ...reqData, organizationId: orgId }
            );
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*-------------------- Pricing by user and sub type --------------------*/

export const PricingByUserAndSubType: any = createAsyncThunk(
    'PricingByUserAndSubType',
    async (reqData: any) => {
        try {
            const response = await postApi(
                PRICING_BY_USER_AND_SUBTYPE,
                reqData
            );
            return response;
        } catch (error: any) {
            console.log('Error', error);
        }
    }
);

/*------------------- Room List by Service Category ------------------ */

export const roomListingByScheduling: any = createAsyncThunk(
    'roomListingByScheduling',
    async (reqData) => {
        try {
            const response = await postApi(ROOM_LIST_BY_SCHEDULING, reqData);
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);

/*------------------- Course list for package ------------------ */

export const courseListForPackage: any = createAsyncThunk(
    'courseListForPackage',
    async (reqData) => {
        try {
            const response = await postApi(COURSE_PACKAGE_LIST, reqData);
            return response;
        } catch (error: any) {
            console.log('Error fetch brand API', error);
            Alertify.error(error.message[0]);
            return Promise.reject(error);
        }
    }
);
