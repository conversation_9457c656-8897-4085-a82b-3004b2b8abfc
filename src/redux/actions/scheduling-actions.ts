import { createAsyncThunk } from '@reduxjs/toolkit';
import dayjs from 'dayjs';
import moment from 'moment';

import {
    CANCEL_SCHEDULING,
    CHECK_IN_SCHEDULING,
    COURSE_SCHEDULING_DETAILS,
    CREATE_SCHEDULING,
    CREATE_TRAINER_AVAILABILITY,
    DELETE_SCHEDULING,
    DELETE_STAFF_TIME_SLOT,
    GET_STAFF_AVAILABILITY_DETAILS,
    GET_STAFF_AVAILABILITY_LIST,
    SCHEDULING_DETAILS,
    SCHEDULING_LIST,
    UPDATE_SCHEDULING,
    UPDATE_TRAINER_AVAILABILITY,
} from '~/constants/api-constant';
import { UserRole } from '~/constants/enums';

import {
    deleteApi,
    getApi,
    handleApiError,
    patchApi,
    postApi,
} from '~/scripts/api-services';
import Alertify from '~/scripts/toast';

export const CreateTrainerAvailability = createAsyncThunk(
    'scheduling/UpdateTrainerAvailability',
    async (
        { availabilityDetails }: any,
        { dispatch, getState, rejectWithValue }: any
    ) => {
        try {
            let { role, userId } = getState().auth_store;
            if (role !== UserRole.Trainer) {
                userId = getState().scheduling_store.selectedTrainer?.userId;
            }
            const res = await postApi(CREATE_TRAINER_AVAILABILITY, {
                ...availabilityDetails,
                userId,
                shifts: undefined,
                reason: availabilityDetails.reason || undefined,
            });
            Alertify.success('Successefully created availability');
            return res;
        } catch (error) {
            return handleApiError(error);
        }
    }
);

export const UpdateTrainerAvailability = createAsyncThunk(
    'scheduling/UpdateTrainerAvailability',
    async (
        { availabilityDetails }: any,
        { dispatch, getState, rejectWithValue }: any
    ) => {
        try {
            let { role, userId } = getState().auth_store;
            if (
                role === UserRole.WEB_MASTER ||
                role === UserRole.FRONT_DESK_ADMIN
            ) {
                userId = getState().scheduling_store.selectedTrainer?.userId;
            }
            const res = await patchApi(UPDATE_TRAINER_AVAILABILITY, {
                ...availabilityDetails,
                userId,
                shifts: undefined,
                reason: availabilityDetails.reason || undefined,
                // markUnavailable: true,
            });
            Alertify.success('Successefully updated availability');
            return res;
        } catch (error) {
            return handleApiError(error);
        }
    }
);

export const GetStaffAvailabilityList = createAsyncThunk(
    'scheduling/GetStaffAvailabilityList',
    async (
        { selectedDate, viewMode, ...payload }: any,
        { dispatch, getState, rejectWithValue }: any
    ) => {
        try {
            const facilityId = getState().facility_store.facility._id;
            console.log('selectedDate', selectedDate);
            // Parse the date string
            const date = moment(selectedDate);

            console.log('payload', payload);
            let startDate, endDate;

            if (viewMode === 'week') {
                startDate = payload.manipulateStartDate;
                endDate = payload.manipulateEndDate;
            } else {
                // Create startDate with time set to 00:00:00.000
                startDate = date
                    .clone()
                    .startOf('day')
                    .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
                // Create endDate with time set to 23:59:59.999
                endDate = date
                    .clone()
                    .endOf('day')
                    .format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
            }

            let { role, userId } = getState().auth_store;
            if (
                role === UserRole.WEB_MASTER ||
                role === UserRole.FRONT_DESK_ADMIN
            ) {
                userId = getState().scheduling_store.selectedTrainer?.userId;
            }
            if (!userId) {
                return;
            }
            const res = await postApi(GET_STAFF_AVAILABILITY_LIST, {
                startDate: startDate,
                endDate: endDate,
                userIds: [userId],
                facilityIds: [facilityId],
            });
            return { res };
        } catch (error) {
            return handleApiError(error);
        }
    }
);

// Function to process the schedule data and update events
export function processScheduleData(schedule = []) {
    const newEvents = schedule.flatMap((day: any) => {
        const date = dayjs(day.date).startOf('day');
        console.log('Day--------', day);

        return day.timeSlots.map((slot: any) => {
            const startTime = dayjs(
                `${date.format('YYYY-MM-DD')}T${slot.from}`
            );
            const endTime = dayjs(`${date.format('YYYY-MM-DD')}T${slot.to}`);

            return {
                title:
                    slot.availabilityStatus === 'available'
                        ? 'Available Slot'
                        : 'Unavailable Slot',
                start: startTime.toDate(),
                end: endTime.toDate(),
                available:
                    slot.availabilityStatus === 'available' ? true : undefined,
                unavailable:
                    slot.availabilityStatus === 'unavailable'
                        ? true
                        : undefined,
                slotId: slot._id,
                from: slot.from,
                to: slot.to,
                classType: slot.classType,
            };
        });
    });

    return newEvents;
}

export const GetStaffAvailabilityDetails = createAsyncThunk(
    'GetStaffAvailabilityDetails',
    async (
        {
            selectedDate,
            dateRange,
            from,
            to,
            availabilityStatus,
            classType,
            endDate: passedEndDate,
        }: any,
        { dispatch, getState, rejectWithValue }: any
    ) => {
        try {
            console.log('selected date details', selectedDate);
            const date = moment(selectedDate);

            console.log('passedEndDate---', passedEndDate);

            // Create startDate with time set to 00:00:00.000
            const startDate = date.clone().startOf('day');

            // Create endDate with time set to 23:59:59.999
            const endDate = passedEndDate
                ? moment(passedEndDate).endOf('day')
                : date.clone().endOf('day');
            const { _id } = getState().facility_store.facility;
            const { userId } = getState().auth_store;
            const { role } = getState().auth_store;
            const { selectedTrainer } = getState().scheduling_store;
            const res = await postApi(GET_STAFF_AVAILABILITY_DETAILS, {
                facilityId: _id,
                trainerId:
                    role === UserRole.Trainer
                        ? userId
                        : selectedTrainer?.userId,
                startDate: startDate.format(),
                endDate: endDate.format(),
                dateRange,
                from,
                // to,
                availabilityStatus,
                classType,
            });
            return { res };
        } catch (error) {
            return handleApiError(error);
        }
    }
);

export const DeleteTimeSlot = createAsyncThunk(
    'DeleteTimeSlot',
    async (
        { payload, selectedDate, facilityId }: any,
        { dispatch, getState }
    ) => {
        try {
            const res = await postApi(DELETE_STAFF_TIME_SLOT, { ...payload });
            await dispatch(
                GetStaffAvailabilityList({
                    selectedDate: selectedDate,
                    facilityId,
                })
            );
            return { res };
        } catch (error) {
            return handleApiError(error);
        }
    }
);

/* --------------- Add book Scheduling ----------------- */

export const CreateBookScheduling: any = createAsyncThunk(
    'staffs/CreateBookScheduling',
    async ({ payload }: any, { rejectWithValue, getState }: any) => {
        try {
            const { organizationId } = getState().auth_store;
            const response = await postApi(CREATE_SCHEDULING, {
                ...payload,
                organizationId,
            });
            Alertify.success('Scheduling booked successfully');
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export const BookedCalendarData: any = createAsyncThunk(
    'staffs/BookedCalendarData',
    async (
        { reqData, filterType }: any,
        { getState, rejectWithValue }: any
    ) => {
        try {
            const { _id } = getState().facility_store.facility;
            const { userId } = getState().auth_store;
            const { role } = getState().auth_store;
            const { selectedTrainer } = getState().scheduling_store;

            const payload: any = {
                facilityId: [_id],
                ...reqData,
                page: 1,
                pageSize: 50,
            };

            if (filterType !== 'room' && filterType !== 'list') {
                payload.trainerId =
                    role === UserRole.Trainer
                        ? [userId]
                        : [selectedTrainer?.userId];
            }

            const response = await postApi(SCHEDULING_LIST, payload);
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/*-------------- Booked Scheduling Details -------------- */

export const BookedSchedulingDetails: any = createAsyncThunk(
    'staffs/BookedSchedulingDetails',
    async ({ scheduleId }: any, { rejectWithValue }: any) => {
        try {
            const response = await getApi(
                `${SCHEDULING_DETAILS}/${scheduleId}`
            );
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/*--------------Course Booked Scheduling Details -------------- */

export const CourseSchedulingDetails: any = createAsyncThunk(
    'staffs/CourseSchedulingDetails',
    async ({ scheduleId }: any, { rejectWithValue }: any) => {
        try {
            const response = await getApi(
                `${COURSE_SCHEDULING_DETAILS}/${scheduleId}`
            );
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/*-------------- Cancel Scheduling -------------- */

export const CancelScheduling: any = createAsyncThunk(
    'staffs/CancelScheduling',
    async ({ scheduleId }: any, { rejectWithValue }: any) => {
        try {
            const response = await patchApi(
                `${CANCEL_SCHEDULING}/${scheduleId}`
            );
            Alertify.success('Booking canceled successfully');
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/*-------------- CheckIn Scheduling -------------- */

export const CheckInScheduling: any = createAsyncThunk(
    'staffs/CheckInScheduling',
    async ({ scheduleId }: any, { rejectWithValue }: any) => {
        try {
            const response = await patchApi(
                `${CHECK_IN_SCHEDULING}/${scheduleId}`
            );
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/*-------------- Booked Scheduling Listing -------------- */

export const DeleteBookedScheduling: any = createAsyncThunk(
    'staffs/DeleteBookedScheduling',
    async ({ id }: any, { rejectWithValue }: any) => {
        try {
            const response = await deleteApi(`${DELETE_SCHEDULING}/${id}`);
            Alertify.success('Scheduling deleted successfully');
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

/*-------------- Booked Scheduling Listing -------------- */

export const UpdateBookedScheduling: any = createAsyncThunk(
    'staffs/UpdateBookedScheduling',
    async ({ payload }: any, { rejectWithValue, getState }: any) => {
        try {
            const { organizationId } = getState().auth_store;
            const response = await patchApi(`${UPDATE_SCHEDULING}`, payload);
            return response;
        } catch (error: any) {
            return handleApiError(error, rejectWithValue);
        }
    }
);

export function ManipulateCalendarData(item: any) {
    const date = new Date(item.date);
    const year = date.getFullYear();
    const month = date.getMonth();
    const day = date.getDate();

    const [fromHour, fromMinute] = item.from.split(':').map(Number);
    const [toHour, toMinute] = item.to.split(':').map(Number);
    return {
        id: item._id,
        title: item.serviceCategoryName,
        trainerName: item.trainerName,
        clientName: item.clientName,
        packageName: item.packageName,
        clientId: item.clientId,
        facilityId: item.facilityId,
        email: item.clientEmail,
        location: item.facilityName,
        serviceCategory: item.serviceCategoryName,
        trainerId: item.trainerId,
        roomId: item.room,
        resourceId: item.trainerId,
        start: new Date(year, month, day, fromHour, fromMinute),
        end: new Date(year, month, day, toHour, toMinute),
        paymentStatus: item.paymentStatus,
        classType: item.classType,
        from: item.from,
        to: item.to,
        date: item.date,
        roomName: item.roomName,
    };
}
