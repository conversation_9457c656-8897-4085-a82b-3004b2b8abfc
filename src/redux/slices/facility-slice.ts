import { createSlice } from '@reduxjs/toolkit';
import {
    FacilityDetails,
    GetAllFacilitiesByOrganization,
    GetAllFacilitiesByStaffId,
    getAllClassTypeByStaffId,
    getAllServiceCategories,
} from '~/redux/actions/facility-actions';

interface initialStateFacility {
    facility: any;
    facilityList: any;
    facilityDetailsData: any;
    classTypes: any;
    serviceCategoryList: any;
}

// Initial state for the slice
const initialState: initialStateFacility = {
    facility: {},
    facilityList: [],
    facilityDetailsData: [],
    classTypes: [],
    serviceCategoryList: [],
};

// Create the slice
const facilitySlice = createSlice({
    name: 'facilitySlice',
    initialState,
    reducers: {
        SetSelectedFacility: (state, { payload }) => {
            state.facility = { ...payload };
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(
                GetAllFacilitiesByOrganization.fulfilled,
                (state, { payload }) => {
                    // Ensure payload.res.data is the correct path
                    console.log('Payload-------------', payload);
                    state.facilityList = payload?.res?.data?.data;
                }
            )
            .addCase(
                GetAllFacilitiesByStaffId.fulfilled,
                (state, { payload }) => {
                    console.log('Payload-------------', payload);
                    state.facilityList = payload.res?.data?.data;
                    if (!Object.keys(state.facility).length > 0) {
                        state.facility = state.facilityList[0];
                    }
                }
            )
            .addCase(FacilityDetails.fulfilled, (state, { payload }) => {
                // console.log('Facility Details-----------', payload);
                state.facilityDetailsData = payload?.data?.data;
            })
            .addCase(
                getAllClassTypeByStaffId.fulfilled,
                (state, { payload }) => {
                    console.log('Payload in classtype------', payload);
                    state.classTypes = payload?.data;
                }
            )
            .addCase(
                getAllServiceCategories.fulfilled,
                (state, { payload }) => {
                    console.log('payload in service------', payload);
                    state.serviceCategoryList = payload?.data || [];
                }
            );
    },
});

export const { SetSelectedFacility } = facilitySlice.actions;

// Export the reducer to be used in the store
export default facilitySlice.reducer;
