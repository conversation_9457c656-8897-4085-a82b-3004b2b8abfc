import { createSlice } from '@reduxjs/toolkit';
import {
    Logout,
    RegisterUser,
    RegisteredUserChangePassword,
    ResetPassword,
    StaffResetPassword,
    UserLogin,
    VerifyOtp,
    organizationList,
} from '~/redux/actions/auth-actions';
import { GetAllFacilitiesByOrganization } from '~/redux/actions/facility-actions';

// import { GetAllFacilitiesByOrganization } from '../actions/facility-actions';

const localState = {
    otpVerificationCode: '',
};

interface AuthState {
    isLogin: boolean;
    token: string;
    userId: string;
    name: string;
    role: string;
    firstName: string;
    lastName: string;
    photo: string;
    facilityList: any[];
    organizationId: string;
    organizationName: string;
    image: string;
    userExist: boolean;
    showOnboardingScreen: boolean;
    facilityId: string;
    onboardingData: any;
    otpVerificationCode: string;
    organizationList: any;
    selectedOrganization: any;
    isPasswordSet: boolean;
}

const initialState: AuthState = {
    isLogin: false,
    token: '',
    userId: '',
    name: '',
    role: '', // default empty role
    firstName: '',
    facilityList: [],
    lastName: '',
    photo: '',
    organizationId: '',
    organizationName: 'Gold Gym',
    image: '',
    userExist: false,
    showOnboardingScreen: false,
    facilityId: '',
    isPasswordSet: true,
    onboardingData: {
        age: '',
        gender: '',
        weight: '',
        height: '',
        goal: '',
        activityLevel: '',
        measurement: {
            chest: '',
            shoulder: '',
            arm: '',
            bicep: '',
            forearm: '',
            waist: '',
            hip: '',
            thigh: '',
            calf: '',
        },
    },
    organizationList: [],
    selectedOrganization: {},

    ...localState,
};

const authSlice = createSlice({
    name: 'auth',
    initialState,
    reducers: {
        setLogin: (state, action) => {
            state.isLogin = action.payload;
        },
        setFacilityID: (state, action) => {
            state.facilityId = action.payload;
        },
        SetOnboardingData: (state, { payload }) => {
            state.onboardingData = { ...state.onboardingData, ...payload };
        },
        ToggleOnboardingStack: (state, { payload }) => {
            state.showOnboardingScreen = payload.state;
        },
        setSelectedOrganization: (state, { payload }) => {
            console.log('payload--------', payload);
            state.selectedOrganization = payload;
        },
        ClearSelectedOrganization: (state) => {
            state.organizationList = [];
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(Logout.fulfilled, () => initialState)
            .addCase(VerifyOtp.fulfilled, (state, { payload }) => {
                state.otpVerificationCode =
                    payload?.res?.data?.data.otpVerificationCode;
                state.userExist = payload.res.data.data.userExist;
                if (state.userExist && !payload.forgotPasswordRequest) {
                    state.isLogin = true;
                }
                if (state.userExist) {
                    state.token = payload.res.data.data.accessToken;
                    state.userId = payload.res.data.data.user._id;
                    state.firstName = payload.res.data.data.user.firstName;
                    state.lastName = payload.res.data.data.user.lastName;
                    state.photo = payload.res.data.data.user.photo;
                    state.role = payload.res.data.data.user.role;
                    state.organizationId = payload.res.data.data.organizationId;
                    state.isPasswordSet = payload.res.data.data.isPasswordSet;
                }
            })
            .addCase(RegisterUser.fulfilled, (state, { payload }) => {
                state.showOnboardingScreen = true;
                state.isLogin = true;
                state.token = payload.res.data.data.accessToken;
                state.userId = payload.res.data.data.user._id;
                state.role = payload.res.data.data.user.role;
            })
            .addCase(UserLogin.fulfilled, (state, { payload }) => {
                console.log('Payload-----------------', payload);
                state.isLogin = true;
                state.userExist = payload.res.data.data.userExist;
                if (state.userExist) {
                    state.token = payload.res.data.data.accessToken;
                    state.userId = payload.res.data.data.user._id;
                    state.firstName = payload.res.data.data.user.firstName;
                    state.lastName = payload.res.data.data.user.lastName;
                    state.photo = payload.res.data.data.user.photo;
                    state.role = payload.res.data.data.user.role;
                }
                state.role = payload.res.data.data?.user?.role;
                state.token = payload.res.data.data.accessToken;
                state.userId = payload.res.data.data.user._id;
                state.firstName = payload.res.data.data.user.firstName;
                state.lastName = payload.res.data.data.user.lastName;
                state.photo = payload.res.data.data.user.photo;
                state.organizationId = payload.res.data.data.organizationId;
                state.isPasswordSet = true;
            })
            .addCase(ResetPassword.fulfilled, (state, { payload }) => {
                state.isLogin = true;
            })
            .addCase(
                RegisteredUserChangePassword.fulfilled,
                (state, { payload }) => {
                    state.isLogin = true;
                }
            )
            .addCase(StaffResetPassword.fulfilled, (state, { payload }) => {
                state.isLogin = true;
            })
            .addCase(
                GetAllFacilitiesByOrganization.fulfilled,
                (state, { payload }) => {
                    state.facilityList = payload.res.data.data;
                }
            )
            .addCase(organizationList.fulfilled, (state, { payload }) => {
                console.log('Payload-----------------', payload);
                state.organizationList = payload?.data?.data?.list;
            });
    },
});

export const {
    setLogin,
    SetOnboardingData,
    ToggleOnboardingStack,
    setFacilityID,
    setSelectedOrganization,
    ClearSelectedOrganization,
} = authSlice.actions;
export default authSlice.reducer;
