import { createNativeStackNavigator } from '@react-navigation/native-stack';

import {
    ACTIVITY_LEVEL,
    APPOINTMENT_LISTING,
    AVA<PERSON>ABILITY_SCREEN,
    BASIC_INFORMATION_SCREEN,
    BOOKING_PAYMENT,
    <PERSON><PERSON><PERSON>_APPOINTMENT,
    <PERSON><PERSON><PERSON>_CLASSES,
    <PERSON><PERSON><PERSON><PERSON>_SCREEN,
    <PERSON><PERSON>IE<PERSON>S_NOTES,
    CLIENT_BASIC_ASSESSMENT,
    CLIENT_BASIC_INFO,
    <PERSON><PERSON>IENT_CHANGE_PASSWORD,
    CLIENT_DETAILS,
    CLIENT_LISTING,
    FACILITY_PROFILE,
    <PERSON><PERSON><PERSON>_SCREEN,
    <PERSON><PERSON>_SCREEN,
    <PERSON><PERSON><PERSON><PERSON>_SCREEN,
    <PERSON><PERSON><PERSON>_SCREEN,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_SCREEN,
    <PERSON><PERSON><PERSON>_SCREEN,
    <PERSON>OR<PERSON>_TAB,
    PROFILE_SCREEN,
    PRO<PERSON>LE_SETTING,
    SELECT_LOCATION_SCREEN,
    STAFF_DETAIL,
    STAFF_LISTING,
    STAFF_PERSONAL_INFO,
    STAF<PERSON>_PERSONAL_INFORMATION,
    STAFF_PROFILE,
    STAFF_SKILL_EXPERIENCE,
    STAFF_SKILL_EXPERIENCE_TAB,
    TEST_SCREEN,
    WAIVER_SCREEN,
    WEIGHT_SCREEN,
} from '~/constants/navigation-constant';

import SelectLocationScreen from '~/screens/auth-screens/select-location-screen';
import testScreen from '~/screens/auth-screens/test-screen';
import CalendarScreen from '~/screens/calendar/calendar-screen';
import AvailabilityScreen from '~/screens/dashboard-screens/availability-screen';
import appointmentListing from '~/screens/dashboard-screens/booking/appointment-listing';
import bookClasses from '~/screens/dashboard-screens/booking/book-classes';
import BookScreen from '~/screens/dashboard-screens/booking/bookScreen';
import BookAppointmentScreen from '~/screens/dashboard-screens/booking/booking-appointment';
import bookingPayment from '~/screens/dashboard-screens/booking/booking-payment';
import HomeScreen from '~/screens/dashboard-screens/home-screen';
import moreScreen from '~/screens/dashboard-screens/more-screen';
import ProfileScreen from '~/screens/dashboard-screens/profile-screen';
import ProfileSetting from '~/screens/dashboard-screens/profile-setting';
import facilityProfile from '~/screens/facility/facility-profile';
import moreTab from '~/screens/more-tab/more-tab';
import ActivityLevel from '~/screens/onboard-screens/activity-level';
import BasicInformation from '~/screens/onboard-screens/basic-information';
import GenderScreen from '~/screens/onboard-screens/gender-screen';
import GoalScreen from '~/screens/onboard-screens/goal-screen';
import HeightScreen from '~/screens/onboard-screens/height-screen';
import MeasurementScreen from '~/screens/onboard-screens/measurement-screen';
import WaiverScreen from '~/screens/onboard-screens/waiver-screen';
import WeightScreen from '~/screens/onboard-screens/weight-screen';
import ClientBasicAssessment from '~/screens/staff-profile/client-basic-assesment';
import ClientBasicInfo from '~/screens/staff-profile/client-basic-info';
import ClientChangesPassword from '~/screens/staff-profile/client-changes-password';
import ClientDetails from '~/screens/staff-profile/client-details';
import ClientListing from '~/screens/staff-profile/client-listing';
import clientsNotes from '~/screens/staff-profile/clients-notes';
import StaffProfile from '~/screens/staff-profile/staff-profile';
import staffBasicInfo from '~/screens/webmaster-profile/staff-basic-info';
import staffDetail from '~/screens/webmaster-profile/staff-detail';
import staffListing from '~/screens/webmaster-profile/staff-listing';
import StaffPersonalInformation from '~/screens/webmaster-profile/staff-personal-info';
import staffSkillExperience from '~/screens/webmaster-profile/staff-skill-experience';
import staffSkillExperienceTab from '~/screens/webmaster-profile/staff-skill-experience-tab';

import { useAppSelector } from '~/hooks/redux-hooks';

const DashboardStack = createNativeStackNavigator();

const DashboardNavigator = () => {
    const store = useAppSelector((state) => ({
        role: state.auth_store.role,
    }));
    return (
        <DashboardStack.Navigator
            screenOptions={{
                headerShown: false,
            }}
            initialRouteName={SELECT_LOCATION_SCREEN}
            // initialRouteName={CALENDAR_SCREEN}
            // initialRouteName={BOOK_CLASSES}
            // initialRouteName={TEST_SCREEN}
            // initialRouteName={STAFF_LISTING}
            // initialRouteName={FACILITY_PROFILE}
        >
            <DashboardStack.Screen
                name={TEST_SCREEN}
                // component={HomeScreen}
                component={testScreen}
            />
            <DashboardStack.Screen
                name={HOME_SCREEN}
                component={HomeScreen}
                // component={TestScreen}
            />
            <DashboardStack.Screen
                name={SELECT_LOCATION_SCREEN}
                component={SelectLocationScreen}
            />
            <DashboardStack.Screen
                name={PROFILE_SCREEN}
                component={ProfileScreen}
            />
            <DashboardStack.Screen
                name={PROFILE_SETTING}
                component={ProfileSetting}
            />
            <DashboardStack.Screen
                name={BASIC_INFORMATION_SCREEN}
                component={BasicInformation}
            />

            <DashboardStack.Screen name={MORE_SCREEN} component={moreScreen} />
            <DashboardStack.Screen
                name={WAIVER_SCREEN}
                component={WaiverScreen}
            />
            <DashboardStack.Screen
                name={GENDER_SCREEN}
                component={GenderScreen}
            />
            <DashboardStack.Screen
                name={WEIGHT_SCREEN}
                component={WeightScreen}
            />
            <DashboardStack.Screen
                name={HEIGHT_SCREEN}
                component={HeightScreen}
            />
            <DashboardStack.Screen name={GOAL_SCREEN} component={GoalScreen} />
            <DashboardStack.Screen
                name={ACTIVITY_LEVEL}
                component={ActivityLevel}
            />
            <DashboardStack.Screen
                name={MEASUREMENT_SCREEN}
                component={MeasurementScreen}
            />
            <DashboardStack.Screen
                name={AVAILABILITY_SCREEN}
                component={AvailabilityScreen}
            />
            <DashboardStack.Screen
                name={STAFF_PROFILE}
                component={StaffProfile}
            />
            <DashboardStack.Screen
                name={CALENDAR_SCREEN}
                component={CalendarScreen}
            />
            <DashboardStack.Screen
                name={BOOK_CLASSES}
                component={bookClasses}
            />
            <DashboardStack.Screen
                name={BOOKING_PAYMENT}
                component={bookingPayment}
            />
            <DashboardStack.Screen
                name={CLIENT_CHANGE_PASSWORD}
                component={ClientChangesPassword}
            />
            <DashboardStack.Screen
                name={CLIENT_LISTING}
                component={ClientListing}
            />
            <DashboardStack.Screen
                name={CLIENT_DETAILS}
                component={ClientDetails}
            />
            <DashboardStack.Screen
                name={CLIENT_BASIC_INFO}
                component={ClientBasicInfo}
            />

            <DashboardStack.Screen
                name={CLIENT_BASIC_ASSESSMENT}
                component={ClientBasicAssessment}
            />
            <DashboardStack.Screen
                name={STAFF_LISTING}
                component={staffListing}
            />
            <DashboardStack.Screen
                name={STAFF_DETAIL}
                component={staffDetail}
            />
            {/* <DashboardStack.Screen
                name={STAFF_BASIC_INFO}
                component={staffBasicInfo}
            /> */}
            <DashboardStack.Screen
                name={STAFF_SKILL_EXPERIENCE}
                component={staffSkillExperience}
            />
            <DashboardStack.Screen
                name={FACILITY_PROFILE}
                component={facilityProfile}
            />
            <DashboardStack.Screen
                name={APPOINTMENT_LISTING}
                component={appointmentListing}
            />
            <DashboardStack.Screen
                name={BOOK_APPOINTMENT}
                component={BookScreen}
            />
            <DashboardStack.Screen
                name={CLIENTS_NOTES}
                component={clientsNotes}
            />
            <DashboardStack.Screen
                name={STAFF_PERSONAL_INFORMATION}
                component={StaffPersonalInformation}
            />
            <DashboardStack.Screen
                name={STAFF_SKILL_EXPERIENCE_TAB}
                component={staffSkillExperienceTab}
            />
            <DashboardStack.Screen name={MORE_TAB} component={moreTab} />
        </DashboardStack.Navigator>
    );
};

export default DashboardNavigator;
